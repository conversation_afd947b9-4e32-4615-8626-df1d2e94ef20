import { z } from 'zod';

export const searchWorkspaceSchema = z.object({
  workspaceId: z.string().uuid('Invalid workspace ID'),
  query: z
    .string()
    .min(1, 'Search query is required')
    .max(500, 'Search query must be less than 500 characters'),
  keywords: z
    .array(z.string())
    .min(1, 'At least one keyword is required')
    .optional(),
  titles: z
    .array(z.string())
    .optional(),
  locations: z
    .array(z.string())
    .min(1, 'At least one location is required')
    .optional(),
  includeVariations: z.boolean().default(true),
  maxResults: z.number().min(1).max(100).default(50)
});

export type SearchWorkspaceSchema = z.infer<typeof searchWorkspaceSchema>;
