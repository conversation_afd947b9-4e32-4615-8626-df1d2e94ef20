{"name": "achromatic-pro", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "dev:email": "email dev", "build": "content-collections build && next build", "build:content": "content-collections build", "start": "next start", "analyze": "BUNDLE_ANALYZE=both next build", "lint": "next lint", "lint:fix": "next lint --fix", "prettier:fix": "prettier --write .", "eslint:fix": "npx eslint --fix .", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "typecheck": "content-collections build && tsc --noEmit", "postinstall": "prisma generate && content-collections build", "stripe:listen": "stripe listen --forward-to http://localhost:3000/api/stripe/webhook"}, "dependencies": {"@auth/prisma-adapter": "2.8.0", "@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@ebay/nice-modal-react": "1.2.13", "@hookform/resolvers": "4.1.3", "@lexical/react": "0.27.1", "@prisma/client": "6.4.1", "@radix-ui/react-accordion": "1.2.3", "@radix-ui/react-alert-dialog": "1.1.6", "@radix-ui/react-aspect-ratio": "1.1.2", "@radix-ui/react-avatar": "1.1.3", "@radix-ui/react-checkbox": "1.1.4", "@radix-ui/react-collapsible": "1.1.3", "@radix-ui/react-context-menu": "2.2.6", "@radix-ui/react-dialog": "1.1.6", "@radix-ui/react-dropdown-menu": "2.1.6", "@radix-ui/react-hover-card": "1.1.6", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.2", "@radix-ui/react-menubar": "1.1.6", "@radix-ui/react-navigation-menu": "1.2.5", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-portal": "1.1.4", "@radix-ui/react-progress": "1.1.2", "@radix-ui/react-radio-group": "1.2.3", "@radix-ui/react-scroll-area": "1.2.3", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-separator": "1.1.2", "@radix-ui/react-slider": "1.2.3", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-switch": "1.1.3", "@radix-ui/react-tabs": "1.1.3", "@radix-ui/react-toast": "1.2.6", "@radix-ui/react-toggle": "1.1.2", "@radix-ui/react-toggle-group": "1.1.2", "@radix-ui/react-tooltip": "1.1.8", "@react-email/components": "0.0.33", "@react-email/render": "1.0.5", "@react-email/tailwind": "1.0.4", "@stripe/stripe-js": "4.8.0", "@tanstack/react-table": "8.21.2", "@types/memory-cache": "0.2.6", "@typeschema/zod": "0.14.0", "bcryptjs": "3.0.2", "class-variance-authority": "0.7.1", "client-only": "0.0.1", "clsx": "2.1.1", "cmdk": "1.0.4", "date-fns": "3.6.0", "embla-carousel-autoplay": "8.5.2", "embla-carousel-react": "8.5.2", "emoji-picker-react": "4.12.0", "exceljs": "4.4.0", "file-saver": "2.0.5", "framer-motion": "12.4.10", "input-otp": "1.4.2", "lexical": "0.27.1", "lucide-react": "0.477.0", "markdown-it": "14.1.0", "mdast-util-toc": "7.1.0", "memory-cache": "0.2.0", "next": "15.2.1", "next-auth": "5.0.0-beta.25", "next-safe-action": "7.10.4", "next-secure-headers": "2.2.0", "next-themes": "0.4.4", "nodemailer": "6.10.0", "nuqs": "2.4.0", "otplib": "12.0.1", "qrcode": "1.5.4", "react": "19.0.0", "react-accessible-treeview": "2.11.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-dropzone": "14.3.8", "react-email": "3.0.7", "react-hook-form": "7.54.2", "react-image-crop": "11.0.7", "react-is": "19.0.0", "react-remove-scroll": "2.6.3", "react-resizable-panels": "2.1.7", "recharts": "2.15.1", "resend": "4.1.2", "sanitize-html": "2.14.0", "server-only": "0.0.1", "sharp": "0.33.5", "sonner": "2.0.1", "stripe": "16.12.0", "tailwind-merge": "3.0.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7", "turndown": "7.2.0", "unist-util-visit": "5.0.0", "uuid": "11.1.0", "vaul": "1.1.2", "vfile": "6.0.3", "zod": "3.24.2"}, "devDependencies": {"@aws-sdk/client-s3": "^3.758.0", "@content-collections/cli": "0.1.6", "@content-collections/core": "0.8.0", "@content-collections/mdx": "0.2.0", "@content-collections/next": "0.2.5", "@eslint/js": "9.21.0", "@ianvs/prettier-plugin-sort-imports": "4.4.1", "@next/bundle-analyzer": "15.2.1", "@svgr/webpack": "8.1.0", "@types/bcryptjs": "2.4.6", "@types/file-saver": "2.0.7", "@types/markdown-it": "14.1.2", "@types/mdast": "4.0.4", "@types/node": "22.13.9", "@types/nodemailer": "6.4.17", "@types/qrcode": "1.5.5", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/sanitize-html": "2.13.0", "@types/turndown": "5.0.5", "@types/unist": "3.0.3", "@types/uuid": "10.0.0", "autoprefixer": "10.4.20", "eslint": "9.21.0", "eslint-config-next": "15.2.1", "eslint-config-prettier": "10.0.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-tailwindcss": "3.18.0", "postcss": "8.5.3", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.11", "prisma": "6.4.1", "rehype": "13.0.2", "rehype-autolink-headings": "7.1.0", "rehype-pretty-code": "0.14.0", "rehype-slug": "6.0.0", "remark": "15.0.1", "remark-code-import": "1.2.0", "remark-gfm": "4.0.1", "shiki": "1.24.3", "typescript": "5.7.2", "typescript-eslint": "8.26.0"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "react-is": "19.0.0", "archiver": "7.0.1", "unzipper": "0.12.3"}, "pnpm": {"overrides": {"react": "19.0.0", "react-dom": "19.0.0", "react-is": "19.0.0", "archiver": "7.0.1", "unzipper": "0.12.3"}}}