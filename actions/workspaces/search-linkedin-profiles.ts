'use server';

import { revalidateTag } from 'next/cache';

import { authActionClient } from '@/actions/safe-action';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { prisma } from '@/lib/db/prisma';
import { searchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';
import { aiDorkService } from '@/lib/services/ai-dork-service';
import { googleSearchService } from '@/lib/services/google-search-service';

export const searchLinkedInProfiles = authActionClient
  .metadata({ actionName: 'searchLinkedInProfiles' })
  .schema(searchWorkspaceSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Get workspace and verify ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organization: {
          users: {
            some: { id: session.user.id }
          }
        }
      },
      select: {
        id: true,
        targetType: true,
        organizationId: true,
        country: true,
        language: true,
        industry: true
      }
    });

    if (!workspace) {
      throw new Error('Workspace not found or access denied');
    }

    // Generate search dorks using AI
    const generatedDorks = await aiDorkService.generateDorks({
      userQuery: parsedInput.query,
      targetType: workspace.targetType,
      locations: parsedInput.locations,
      industry: workspace.industry || undefined,
      country: workspace.country || undefined,
      language: workspace.language || undefined,
      includeVariations: parsedInput.includeVariations,
      maxDorks: 5
    });

    const allResults: Array<{
      url: string;
      title: string;
      description: string;
      foundInSearch: string;
      position: number;
      type: 'profile' | 'company';
    }> = [];

    // Execute searches
    for (const generatedDork of generatedDorks) {
      try {
        // Create search record
        const searchRecord = await prisma.workspaceSearch.create({
          data: {
            workspaceId: workspace.id,
            query: parsedInput.query,
            searchDork: generatedDork.dork,
            status: 'RUNNING'
          }
        });

        // Execute Google search
        const searchResult = await googleSearchService.searchLinkedInProfiles(
          generatedDork.dork,
          {
            maxResults: parsedInput.maxResults || 50,
            country: workspace.country || undefined,
            language: workspace.language || undefined
            // Removed timeFilter to get more results
          }
        );

        // Extract LinkedIn URLs
        const linkedinResults = googleSearchService.extractLinkedInUrls(searchResult);

        // Process results
        for (const result of linkedinResults) {
          // Normalize URL
          const normalizedUrl = googleSearchService.normalizeLinkedInUrl(result.url);

          allResults.push({
            url: normalizedUrl,
            title: result.title,
            description: result.description,
            foundInSearch: generatedDork.dork,
            position: result.position,
            type: result.type
          });
        }

        // Update search status
        await prisma.workspaceSearch.update({
          where: { id: searchRecord.id },
          data: {
            status: 'COMPLETED',
            resultsCount: linkedinResults.length,
            executedAt: new Date()
          }
        });

        // Add delay between searches to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error('Search failed for dork:', generatedDork.dork, error);

        // Update search status to failed
        await prisma.workspaceSearch.updateMany({
          where: {
            workspaceId: workspace.id,
            searchDork: generatedDork.dork,
            status: 'RUNNING'
          },
          data: {
            status: 'FAILED'
          }
        });
      }
    }

    // Remove duplicates based on URL
    const uniqueResults = allResults.reduce((acc, current) => {
      const existing = acc.find(item => item.url === current.url);
      if (!existing) {
        acc.push(current);
      }
      return acc;
    }, [] as typeof allResults);

    // Add profiles to workspace (with deduplication)
    const addedProfiles = [];

    for (const result of uniqueResults) {
      // Validate LinkedIn URL
      if (!googleSearchService.isValidLinkedInUrl(result.url)) {
        continue;
      }

      // Check if profile already exists
      let profile = await prisma.linkedInProfile.findUnique({
        where: { linkedinUrl: result.url }
      });

      // Create profile if it doesn't exist
      if (!profile) {
        profile = await prisma.linkedInProfile.create({
          data: {
            linkedinUrl: result.url,
            profileData: null // Will be populated when user unlocks
          }
        });
      }

      // Check if already in workspace
      const existingConnection = await prisma.workspaceProfile.findUnique({
        where: {
          workspaceId_profileId: {
            workspaceId: workspace.id,
            profileId: profile.id
          }
        }
      });

      // Add to workspace if not already there
      if (!existingConnection) {
        await prisma.workspaceProfile.create({
          data: {
            workspaceId: workspace.id,
            profileId: profile.id,
            foundInSearch: result.foundInSearch,
            title: result.title,
            description: result.description
          }
        });

        addedProfiles.push({
          id: profile.id,
          url: result.url,
          title: result.title,
          description: result.description,
          type: result.type,
          position: result.position
        });
      }
    }

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.WorkspaceProfiles,
        workspace.organizationId,
        workspace.id
      )
    );

    return {
      totalFound: allResults.length,
      uniqueFound: uniqueResults.length,
      newProfiles: addedProfiles.length,
      profiles: addedProfiles,
      searchDorks: generatedDorks.map(d => d.dork)
    };
  });
