'use server';

import { revalidateTag } from 'next/cache';

import { authActionClient } from '@/actions/safe-action';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { prisma } from '@/lib/db/prisma';
import { searchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';

// Mock Google Search API response type (linkedin-data.json format)
interface GoogleSearchResult {
  search_term: string;
  results: Array<{
    position: number;
    url: string;
    title: string;
    description: string;
  }>;
  next_page?: number;
  next_start?: number;
}

// Mock AI service for generating search dorks
async function generateSearchDorks(
  query: string,
  targetType: 'PERSON' | 'COMPANY',
  locations?: string[]
): Promise<string[]> {
  // This would call OpenAI API in real implementation
  const baseQuery = query.toLowerCase();
  const site = targetType === 'PERSON' ? 'site:linkedin.com/in' : 'site:linkedin.com/company';
  
  const dorks = [];
  
  if (locations && locations.length > 0) {
    for (const location of locations) {
      if (targetType === 'PERSON') {
        dorks.push(`${site} "${baseQuery}" ("kurucu" OR "owner" OR "founder" OR "CEO") "${location}"`);
        dorks.push(`${site} "${baseQuery}" ("müdür" OR "director") "${location}"`);
      } else {
        dorks.push(`${site} "${baseQuery}" "${location}"`);
      }
    }
  } else {
    if (targetType === 'PERSON') {
      dorks.push(`${site} "${baseQuery}" ("kurucu" OR "owner" OR "founder")`);
    } else {
      dorks.push(`${site} "${baseQuery}"`);
    }
  }
  
  return dorks;
}

// Mock Google Search API call
async function searchGoogle(dork: string): Promise<GoogleSearchResult> {
  // This would call the actual Google Search API
  // For now, return mock data similar to linkedin-data.json
  return {
    search_term: dork,
    results: [
      {
        position: 1,
        url: "https://tr.linkedin.com/in/example-profile",
        title: "Example Profile - LinkedIn",
        description: "Example description..."
      }
    ]
  };
}

export const searchLinkedInProfiles = authActionClient
  .metadata({ actionName: 'searchLinkedInProfiles' })
  .schema(searchWorkspaceSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Get workspace and verify ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organization: {
          users: {
            some: { id: session.user.id }
          }
        }
      },
      select: {
        id: true,
        targetType: true,
        organizationId: true
      }
    });

    if (!workspace) {
      throw new Error('Workspace not found or access denied');
    }

    // Generate search dorks using AI
    const dorks = await generateSearchDorks(
      parsedInput.query,
      workspace.targetType,
      parsedInput.locations
    );

    const allResults: Array<{
      url: string;
      title: string;
      description: string;
      foundInSearch: string;
    }> = [];

    // Execute searches
    for (const dork of dorks) {
      try {
        // Create search record
        const searchRecord = await prisma.workspaceSearch.create({
          data: {
            workspaceId: workspace.id,
            query: parsedInput.query,
            searchDork: dork,
            status: 'RUNNING'
          }
        });

        // Execute Google search
        const searchResult = await searchGoogle(dork);
        
        // Process results
        for (const result of searchResult.results) {
          if (result.url.includes('linkedin.com/in') || result.url.includes('linkedin.com/company')) {
            allResults.push({
              url: result.url,
              title: result.title,
              description: result.description,
              foundInSearch: dork
            });
          }
        }

        // Update search status
        await prisma.workspaceSearch.update({
          where: { id: searchRecord.id },
          data: {
            status: 'COMPLETED',
            resultsCount: searchResult.results.length,
            executedAt: new Date()
          }
        });

      } catch (error) {
        console.error('Search failed:', error);
        // Update search status to failed
        // Handle error appropriately
      }
    }

    // Add profiles to workspace (with deduplication)
    const addedProfiles = [];
    
    for (const result of allResults) {
      // Check if profile already exists
      let profile = await prisma.linkedInProfile.findUnique({
        where: { linkedinUrl: result.url }
      });

      // Create profile if it doesn't exist
      if (!profile) {
        profile = await prisma.linkedInProfile.create({
          data: {
            linkedinUrl: result.url,
            profileData: null // Will be populated when user unlocks
          }
        });
      }

      // Check if already in workspace
      const existingConnection = await prisma.workspaceProfile.findUnique({
        where: {
          workspaceId_profileId: {
            workspaceId: workspace.id,
            profileId: profile.id
          }
        }
      });

      // Add to workspace if not already there
      if (!existingConnection) {
        await prisma.workspaceProfile.create({
          data: {
            workspaceId: workspace.id,
            profileId: profile.id,
            foundInSearch: result.foundInSearch,
            title: result.title,
            description: result.description
          }
        });

        addedProfiles.push({
          id: profile.id,
          url: result.url,
          title: result.title,
          description: result.description
        });
      }
    }

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.WorkspaceProfiles,
        workspace.organizationId,
        workspace.id
      )
    );

    return {
      totalFound: allResults.length,
      newProfiles: addedProfiles.length,
      profiles: addedProfiles
    };
  });
