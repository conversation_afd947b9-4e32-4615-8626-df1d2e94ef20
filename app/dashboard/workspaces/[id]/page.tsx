import * as React from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon, SearchIcon, UserIcon, BuildingIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Page, PageBody, PageHeader, PagePrimaryBar, PageTitle, PageActions } from '@/components/ui/page';
import { Routes } from '@/constants/routes';
import { getWorkspaces } from '@/data/workspaces/get-workspaces';
import { getWorkspaceProfiles } from '@/data/workspaces/get-workspace-profiles';
import { WorkspaceSearchForm } from '@/components/dashboard/workspaces/workspace-search-form';
import { WorkspaceProfileList } from '@/components/dashboard/workspaces/workspace-profile-list';
import { formatDistanceToNow } from 'date-fns';

interface WorkspacePageProps {
  params: {
    id: string;
  };
}

export default async function WorkspacePage({ params }: WorkspacePageProps) {
  const workspaces = await getWorkspaces();
  const workspace = workspaces.find(w => w.id === params.id);

  if (!workspace) {
    notFound();
  }

  const profiles = await getWorkspaceProfiles(params.id);

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild>
              <Link href={Routes.Workspaces}>
                <ArrowLeftIcon className="size-4" />
                Back
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              {workspace.targetType === 'PERSON' ? (
                <UserIcon className="size-5 text-blue-600" />
              ) : (
                <BuildingIcon className="size-5 text-green-600" />
              )}
              <PageTitle>{workspace.name}</PageTitle>
            </div>
          </div>
          <PageActions>
            <Badge variant={workspace.targetType === 'PERSON' ? 'default' : 'secondary'}>
              {workspace.targetType === 'PERSON' ? 'People' : 'Companies'}
            </Badge>
          </PageActions>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <div className="mx-auto max-w-7xl space-y-6 p-6">
          {/* Workspace Info */}
          <div className="rounded-lg border bg-card p-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Target Type</div>
                <div className="text-lg font-semibold">
                  {workspace.targetType === 'PERSON' ? 'People' : 'Companies'}
                </div>
              </div>
              {workspace.industry && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Industry</div>
                  <div className="text-lg font-semibold">{workspace.industry}</div>
                </div>
              )}
              {workspace.country && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Country</div>
                  <div className="text-lg font-semibold">{workspace.country.toUpperCase()}</div>
                </div>
              )}
              <div>
                <div className="text-sm font-medium text-muted-foreground">Profiles Found</div>
                <div className="text-lg font-semibold">{workspace._count.profiles}</div>
              </div>
            </div>
            {workspace.description && (
              <div className="mt-4">
                <div className="text-sm font-medium text-muted-foreground">Description</div>
                <div className="mt-1 text-sm">{workspace.description}</div>
              </div>
            )}
            <div className="mt-4 text-xs text-muted-foreground">
              Created {formatDistanceToNow(workspace.createdAt, { addSuffix: true })} • 
              Updated {formatDistanceToNow(workspace.updatedAt, { addSuffix: true })}
            </div>
          </div>

          {/* Search Form */}
          <div className="rounded-lg border bg-card">
            <div className="border-b p-4">
              <div className="flex items-center gap-2">
                <SearchIcon className="size-5 text-muted-foreground" />
                <h2 className="text-lg font-semibold">Search LinkedIn Profiles</h2>
              </div>
              <p className="mt-1 text-sm text-muted-foreground">
                Use AI-powered search to find {workspace.targetType === 'PERSON' ? 'professionals' : 'companies'} on LinkedIn
              </p>
            </div>
            <div className="p-4">
              <WorkspaceSearchForm 
                workspaceId={workspace.id} 
                targetType={workspace.targetType}
                defaultCountry={workspace.country || undefined}
                defaultLanguage={workspace.language || undefined}
              />
            </div>
          </div>

          {/* Profile Results */}
          <div className="rounded-lg border bg-card">
            <div className="border-b p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">Found Profiles</h2>
                  <p className="text-sm text-muted-foreground">
                    {profiles.length} {workspace.targetType === 'PERSON' ? 'profiles' : 'companies'} found
                  </p>
                </div>
                {profiles.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Click profiles to unlock detailed information
                  </div>
                )}
              </div>
            </div>
            <div className="p-4">
              <WorkspaceProfileList 
                profiles={profiles}
                targetType={workspace.targetType}
              />
            </div>
          </div>
        </div>
      </PageBody>
    </Page>
  );
}
