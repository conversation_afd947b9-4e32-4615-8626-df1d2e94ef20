import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';
import { linkedInBulkScraperService } from '@/lib/services/linkedin-bulk-scraper-service';
import { z } from 'zod';

const bulkScrapeSchema = z.object({
  workspaceId: z.string().uuid(),
  linkedinUrls: z.array(z.string().url()).min(1).max(10)
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const parsedInput = bulkScrapeSchema.parse(body);

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    console.log('Bulk scraping request:', {
      workspaceId: parsedInput.workspaceId,
      profileCount: parsedInput.linkedinUrls.length,
      organizationId: session.user.organizationId
    });

    // Step 1: Scrape LinkedIn profiles
    const scrapingResult = await linkedInBulkScraperService.scrapeProfiles(parsedInput.linkedinUrls);

    if (!scrapingResult.success) {
      return NextResponse.json({ 
        error: 'Failed to scrape profiles',
        details: scrapingResult 
      }, { status: 500 });
    }

    // Step 2: Update LinkedIn profiles in database
    const updatedProfiles = [];
    
    for (const profileResult of scrapingResult.data) {
      try {
        // Update LinkedIn profile with scraped data
        const updatedProfile = await prisma.linkedInProfile.update({
          where: { linkedinUrl: profileResult.entry },
          data: {
            profileData: profileResult.data,
            scrapedAt: new Date()
          }
        });

        updatedProfiles.push(updatedProfile);

        // Step 3: Background enrichment (company + email scraping)
        // Don't await this - let it run in background
        enrichProfileInBackground(profileResult.data, session.user.organizationId, profileResult.entry)
          .catch(error => {
            console.error('Background enrichment failed:', error);
          });

      } catch (error) {
        console.error('Failed to update profile:', profileResult.entry, error);
      }
    }

    return NextResponse.json({
      success: true,
      creditsUsed: scrapingResult.credits_used,
      profilesScraped: updatedProfiles.length,
      profiles: updatedProfiles
    });

  } catch (error) {
    console.error('Bulk scraping error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Filter emails to only include those from the target domain
 */
function filterEmailsByDomain(emails: any[], targetDomain: string): any[] {
  if (!emails || !targetDomain) return [];

  return emails.filter(email => {
    if (!email.sources || !Array.isArray(email.sources)) return false;

    // Check if any source URL belongs to the target domain
    return email.sources.some((source: string) => {
      try {
        const sourceUrl = new URL(source);
        const sourceDomain = sourceUrl.hostname.replace('www.', '').toLowerCase();
        const normalizedTargetDomain = targetDomain.replace('www.', '').toLowerCase();

        // Must be from the exact target domain (not subdomains or other domains)
        return sourceDomain === normalizedTargetDomain;
      } catch {
        return false;
      }
    });
  });
}

/**
 * Background enrichment: Company data + Email scraping
 */
async function enrichProfileInBackground(profileData: any, organizationId: string, linkedinUrl: string) {
  try {
    console.log('Starting background enrichment for:', profileData.fullName);

    // Step 1: Get current company from experiences
    const currentExperience = profileData.experiences?.[0];
    if (!currentExperience) {
      console.log('No current experience found');
      return;
    }

    let companyData = null;
    let emailData = null;

    // Step 2: Scrape company data if companyId exists
    const companyId = linkedInBulkScraperService.extractCompanyId(currentExperience);
    if (companyId) {
      try {
        console.log('Scraping company data for:', companyId);
        const companyResult = await linkedInBulkScraperService.scrapeCompany(companyId);
        console.log('Company scraping result:', { success: companyResult.success, hasData: !!companyResult.data });
        if (companyResult.success) {
          companyData = companyResult.data;
        }
      } catch (error) {
        console.error('Company scraping failed:', error);
      }
    }

    // Step 3: Scrape emails if company has website
    if (companyData?.website) {
      const domain = linkedInBulkScraperService.extractDomain(companyData.website);
      if (domain) {
        try {
          console.log('Scraping emails for domain:', domain);
          const emailResult = await linkedInBulkScraperService.scrapeEmails(domain);
          if (emailResult.status === 'OK' && emailResult.data?.[0]?.emails) {
            // Filter emails to only include those from the target domain
            const filteredEmails = filterEmailsByDomain(emailResult.data[0].emails, domain);
            const filteredPhones = emailResult.data[0].phone_numbers || [];

            if (filteredEmails.length > 0) {
              emailData = {
                ...emailResult.data[0],
                emails: filteredEmails,
                phone_numbers: filteredPhones
              };
              console.log(`Filtered emails: ${filteredEmails.length} from ${emailResult.data[0].emails.length} total`);
            }
          }
        } catch (error) {
          console.error('Email scraping failed:', error);
        }
      }
    }

    // Step 4: Create Contact from LinkedIn profile
    await createContactFromLinkedInProfile(profileData, companyData, emailData, organizationId, linkedinUrl);

    console.log('Background enrichment completed for:', profileData.fullName);

  } catch (error) {
    console.error('Background enrichment error:', error);
  }
}

/**
 * Generate smart tags from LinkedIn profile data
 */
function generateSmartTags(profileData: any) {
  const allTags = [];

  // 1. Extract job titles from headline
  if (profileData.headline && profileData.headline.trim() !== '--') {
    const headline = profileData.headline.trim();
    const keywords = extractKeywordsFromHeadline(headline);

    keywords.forEach(keyword => {
      if (isValidTag(keyword)) {
        allTags.push({
          text: keyword,
          priority: 3, // High priority for headline keywords
          source: 'headline'
        });
      }
    });
  }

  // 2. Extract job title from current experience
  if (profileData.experiences && profileData.experiences.length > 0) {
    const currentJob = profileData.experiences[0];
    if (currentJob.title) {
      const jobTitle = extractJobTitle(currentJob.title);
      if (jobTitle && isValidTag(jobTitle)) {
        allTags.push({
          text: jobTitle,
          priority: 2, // Medium priority for job title
          source: 'experience'
        });
      }
    }
  }

  // 3. Extract location (city only)
  if (profileData.addressWithoutCountry) {
    const location = extractCityFromAddress(profileData.addressWithoutCountry);
    if (location && isValidTag(location)) {
      allTags.push({
        text: location,
        priority: 1, // Low priority for location
        source: 'location'
      });
    }
  }

  // 4. Extract industry from education or skills
  if (profileData.educations && profileData.educations.length > 0) {
    const education = profileData.educations[0];
    if (education.subtitle) {
      const field = extractEducationField(education.subtitle);
      if (field && isValidTag(field)) {
        allTags.push({
          text: field,
          priority: 1, // Low priority for education
          source: 'education'
        });
      }
    }
  }

  // 5. Sort by priority and remove duplicates
  const uniqueTags = [];
  const seenTexts = new Set();

  allTags
    .sort((a, b) => b.priority - a.priority) // High priority first
    .forEach(tag => {
      const normalizedText = tag.text.toLowerCase();
      if (!seenTexts.has(normalizedText) && uniqueTags.length < 3) {
        seenTexts.add(normalizedText);
        uniqueTags.push(tag);
      }
    });

  // 6. Ensure minimum 1 tag
  if (uniqueTags.length === 0) {
    // Fallback: use first word of name as tag
    const firstName = profileData.firstName || profileData.fullName?.split(' ')[0] || 'Contact';
    if (firstName.length >= 3) {
      uniqueTags.push({
        text: firstName,
        priority: 0,
        source: 'fallback'
      });
    }
  }

  // Convert to Prisma format
  return uniqueTags.map(tag => ({
    where: { text: tag.text },
    create: { text: tag.text }
  }));
}

/**
 * Extract city from address
 */
function extractCityFromAddress(address: string): string | null {
  if (!address) return null;

  // Split by comma and take first part (usually city)
  const parts = address.split(',').map(part => part.trim());
  const city = parts[0];

  if (city && city.length >= 3 && city.length <= 20) {
    return city;
  }

  return null;
}

/**
 * Extract education field
 */
function extractEducationField(subtitle: string): string | null {
  if (!subtitle) return null;

  // Common education fields
  const fields = [
    'diyetetik', 'dietetics', 'beslenme', 'nutrition',
    'mühendislik', 'engineering', 'tıp', 'medicine',
    'hemşirelik', 'nursing', 'eczacılık', 'pharmacy',
    'fizyoterapi', 'physiotherapy', 'psikoloji', 'psychology'
  ];

  const lowerSubtitle = subtitle.toLowerCase();

  for (const field of fields) {
    if (lowerSubtitle.includes(field)) {
      return field;
    }
  }

  return null;
}

/**
 * Check if a text is valid for tagging
 */
function isValidTag(text: string): boolean {
  if (!text || text.length < 3 || text.length > 20) return false;

  // Skip company names and long descriptions
  const lowerText = text.toLowerCase();

  // Skip if contains company indicators
  if (lowerText.includes('şirket') ||
      lowerText.includes('company') ||
      lowerText.includes('ltd') ||
      lowerText.includes('a.ş') ||
      lowerText.includes('danışmanlık') ||
      lowerText.includes('merkezi') ||
      lowerText.includes('kliniği') ||
      lowerText.includes('hastanesi') ||
      lowerText.includes('enstitü') ||
      lowerText.includes('center') ||
      lowerText.includes('clinic') ||
      lowerText.includes('institute') ||
      lowerText.includes('university') ||
      lowerText.includes('üniversite')) {
    return false;
  }

  // Skip if too many words (likely a company name)
  const wordCount = text.trim().split(/\s+/).length;
  if (wordCount > 2) return false;

  // Skip if contains numbers (likely company names)
  if (/\d/.test(text)) return false;

  // Skip common meaningless words
  const meaninglessWords = ['at', 'in', 'the', 'and', 've', 'ile', 'için', 'olan', 'bir'];
  if (meaninglessWords.includes(lowerText)) return false;

  return true;
}

/**
 * Extract meaningful keywords from headline
 */
function extractKeywordsFromHeadline(headline: string): string[] {
  const keywords = [];

  // Split by common separators
  const parts = headline.split(/[|•·\-\n\r]+/).map(part => part.trim());

  for (const part of parts) {
    if (part.length < 3) continue;

    // Clean up the part
    let cleaned = part
      .replace(/^(at|şirketinde|'te|'da|'de)\s+/i, '') // Remove "at", "şirketinde" etc.
      .replace(/\s+(at|şirketinde|'te|'da|'de)$/i, '') // Remove trailing "at", "şirketinde" etc.
      .replace(/[()[\]{}]/g, '') // Remove brackets
      .replace(/\s+/g, ' ') // Normalize spaces
      .trim();

    // Skip if contains unwanted patterns
    if (cleaned.includes('...') || cleaned.includes('www.')) continue;

    // Extract job titles/roles only
    const jobTitles = extractJobTitles(cleaned);
    keywords.push(...jobTitles);
  }

  return keywords;
}

/**
 * Extract job titles from text
 */
function extractJobTitles(text: string): string[] {
  const titles = [];

  // Common job title patterns
  const jobPatterns = [
    /\b(kurucu|founder|co-founder)\b/gi,
    /\b(diyetisyen|dietitian|nutritionist)\b/gi,
    /\b(müdür|director|manager|yönetici)\b/gi,
    /\b(uzman|specialist|expert)\b/gi,
    /\b(danışman|consultant|advisor)\b/gi,
    /\b(doktor|doctor|dr\.?)\b/gi,
    /\b(mimar|architect)\b/gi,
    /\b(mühendis|engineer)\b/gi,
    /\b(avukat|lawyer)\b/gi,
    /\b(öğretmen|teacher|educator)\b/gi,
    /\b(ceo|cto|cfo|cmo)\b/gi
  ];

  // Extract individual words that match job patterns
  const words = text.split(/\s+/);

  for (const word of words) {
    const cleanWord = word.replace(/[^\w\s]/g, '').trim();

    if (cleanWord.length >= 3 && cleanWord.length <= 15) {
      // Check if it matches job patterns
      for (const pattern of jobPatterns) {
        if (pattern.test(cleanWord)) {
          titles.push(cleanWord.toLowerCase());
          break;
        }
      }
    }
  }

  // Also add compound job titles (max 2 words)
  const compounds = text.match(/\b(kurucu\s+diyetisyen|beslenme\s+uzmanı|dijital\s+pazarlama|insan\s+kaynakları)\b/gi);
  if (compounds) {
    titles.push(...compounds.map(c => c.toLowerCase()));
  }

  return [...new Set(titles)]; // Remove duplicates
}

/**
 * Extract clean job title
 */
function extractJobTitle(title: string): string | null {
  if (!title) return null;

  // Clean up title
  let cleaned = title
    .replace(/^(at|şirketinde|'te|'da|'de)\s+/i, '')
    .replace(/\s+(at|şirketinde|'te|'da|'de)$/i, '')
    .replace(/[()[\]{}]/g, '')
    .trim();

  // Return if reasonable length
  if (cleaned.length >= 3 && cleaned.length <= 50) {
    return cleaned;
  }

  return null;
}

/**
 * Create Contact from LinkedIn profile data
 */
async function createContactFromLinkedInProfile(
  profileData: any,
  companyData: any,
  emailData: any,
  organizationId: string,
  linkedinUrl?: string
) {
  try {
    // Check if contact already exists
    const existingContact = await prisma.contact.findFirst({
      where: {
        organizationId,
        name: profileData.fullName
      }
    });

    if (existingContact) {
      console.log('Contact already exists:', profileData.fullName);
      return existingContact;
    }

    // Create new contact
    const contact = await prisma.contact.create({
      data: {
        organizationId,
        record: 'PERSON',
        name: profileData.fullName,
        address: profileData.addressWithCountry,
        image: profileData.profilePic,
        stage: 'LEAD',
        linkedinUrl: linkedinUrl,
        
        // Create activity for LinkedIn scrape
        activities: {
          create: {
            actionType: 'CREATE',
            actorType: 'SYSTEM',
            actorId: organizationId, // Use organizationId as actorId for system actions
            metadata: {
              source: 'linkedin_bulk_scrape',
              profileData,
              companyData,
              emailData,
              scrapedAt: new Date()
            }
          }
        },

        // Add smart tags
        tags: {
          connectOrCreate: generateSmartTags(profileData)
        }
      }
    });

    // Add emails if found
    if (emailData?.emails?.length > 0) {
      const emailPromises = emailData.emails.slice(0, 5).map((email: any, index: number) =>
        prisma.contactEmail.create({
          data: {
            contactId: contact.id,
            email: email.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: email.sources
          }
        }).catch(error => {
          console.error('Failed to create email:', email.value, error);
        })
      );

      await Promise.all(emailPromises);

      // Update contact's primary email
      const primaryEmail = emailData.emails[0]?.value;
      if (primaryEmail) {
        await prisma.contact.update({
          where: { id: contact.id },
          data: { email: primaryEmail }
        });
      }
    }

    // Add phones if found
    if (emailData?.phone_numbers?.length > 0) {
      const phonePromises = emailData.phone_numbers.slice(0, 3).map((phone: any, index: number) =>
        prisma.contactPhone.create({
          data: {
            contactId: contact.id,
            phone: phone.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: phone.sources
          }
        }).catch(error => {
          console.error('Failed to create phone:', phone.value, error);
        })
      );

      await Promise.all(phonePromises);

      // Update contact's primary phone
      const primaryPhone = emailData.phone_numbers[0]?.value;
      if (primaryPhone) {
        await prisma.contact.update({
          where: { id: contact.id },
          data: { phone: primaryPhone }
        });
      }
    }

    console.log('Contact created successfully:', {
      id: contact.id,
      name: contact.name,
      organizationId: contact.organizationId,
      emailsAdded: emailData?.emails?.length || 0,
      phonesAdded: emailData?.phone_numbers?.length || 0,
      companyName: companyData?.company_name || 'N/A',
      companyWebsite: companyData?.website || 'N/A'
    });
    return contact;

  } catch (error) {
    console.error('Failed to create contact:', error);
    throw error;
  }
}
