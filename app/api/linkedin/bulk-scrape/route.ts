import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';
import { linkedInBulkScraperService } from '@/lib/services/linkedin-bulk-scraper-service';
import { z } from 'zod';

const bulkScrapeSchema = z.object({
  workspaceId: z.string().uuid(),
  linkedinUrls: z.array(z.string().url()).min(1).max(10)
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const parsedInput = bulkScrapeSchema.parse(body);

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    console.log('Bulk scraping request:', {
      workspaceId: parsedInput.workspaceId,
      profileCount: parsedInput.linkedinUrls.length,
      organizationId: session.user.organizationId
    });

    // Step 1: Scrape LinkedIn profiles
    const scrapingResult = await linkedInBulkScraperService.scrapeProfiles(parsedInput.linkedinUrls);

    if (!scrapingResult.success) {
      return NextResponse.json({ 
        error: 'Failed to scrape profiles',
        details: scrapingResult 
      }, { status: 500 });
    }

    // Step 2: Update LinkedIn profiles in database
    const updatedProfiles = [];
    
    for (const profileResult of scrapingResult.data) {
      try {
        // Update LinkedIn profile with scraped data
        const updatedProfile = await prisma.linkedInProfile.update({
          where: { linkedinUrl: profileResult.entry },
          data: {
            profileData: profileResult.data,
            scrapedAt: new Date()
          }
        });

        updatedProfiles.push(updatedProfile);

        // Step 3: Background enrichment (company + email scraping)
        // Don't await this - let it run in background
        enrichProfileInBackground(profileResult.data, session.user.organizationId)
          .catch(error => {
            console.error('Background enrichment failed:', error);
          });

      } catch (error) {
        console.error('Failed to update profile:', profileResult.entry, error);
      }
    }

    return NextResponse.json({
      success: true,
      creditsUsed: scrapingResult.credits_used,
      profilesScraped: updatedProfiles.length,
      profiles: updatedProfiles
    });

  } catch (error) {
    console.error('Bulk scraping error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Background enrichment: Company data + Email scraping
 */
async function enrichProfileInBackground(profileData: any, organizationId: string) {
  try {
    console.log('Starting background enrichment for:', profileData.fullName);

    // Step 1: Get current company from experiences
    const currentExperience = profileData.experiences?.[0];
    if (!currentExperience) {
      console.log('No current experience found');
      return;
    }

    let companyData = null;
    let emailData = null;

    // Step 2: Scrape company data if companyId exists
    const companyId = linkedInBulkScraperService.extractCompanyId(currentExperience);
    if (companyId) {
      try {
        console.log('Scraping company data for:', companyId);
        const companyResult = await linkedInBulkScraperService.scrapeCompany(companyId);
        if (companyResult.success) {
          companyData = companyResult.data;
        }
      } catch (error) {
        console.error('Company scraping failed:', error);
      }
    }

    // Step 3: Scrape emails if company has website
    if (companyData?.website) {
      const domain = linkedInBulkScraperService.extractDomain(companyData.website);
      if (domain) {
        try {
          console.log('Scraping emails for domain:', domain);
          const emailResult = await linkedInBulkScraperService.scrapeEmails(domain);
          if (emailResult.status === 'OK' && emailResult.data?.[0]?.emails) {
            emailData = emailResult.data[0];
          }
        } catch (error) {
          console.error('Email scraping failed:', error);
        }
      }
    }

    // Step 4: Create Contact from LinkedIn profile
    await createContactFromLinkedInProfile(profileData, companyData, emailData, organizationId);

    console.log('Background enrichment completed for:', profileData.fullName);

  } catch (error) {
    console.error('Background enrichment error:', error);
  }
}

/**
 * Create Contact from LinkedIn profile data
 */
async function createContactFromLinkedInProfile(
  profileData: any,
  companyData: any,
  emailData: any,
  organizationId: string
) {
  try {
    // Check if contact already exists (by name only for now)
    const existingContact = await prisma.contact.findFirst({
      where: {
        organizationId,
        name: profileData.fullName
      }
    });

    if (existingContact) {
      console.log('Contact already exists:', profileData.fullName);
      return existingContact;
    }

    // Create new contact
    const contact = await prisma.contact.create({
      data: {
        organizationId,
        record: 'PERSON',
        name: profileData.fullName,
        address: profileData.addressWithCountry,
        image: profileData.profilePic,
        stage: 'LEAD',
        
        // Create activity for LinkedIn scrape
        activities: {
          create: {
            actionType: 'CREATE',
            actorType: 'SYSTEM',
            metadata: {
              source: 'linkedin_bulk_scrape',
              profileData,
              companyData,
              emailData,
              scrapedAt: new Date()
            }
          }
        },

        // Add tags
        tags: {
          connectOrCreate: [
            { where: { text: 'LinkedIn' }, create: { text: 'LinkedIn' } },
            ...(profileData.headline ? [{ 
              where: { text: profileData.headline.slice(0, 100) }, 
              create: { text: profileData.headline.slice(0, 100) } 
            }] : [])
          ]
        }
      }
    });

    // Add emails if found
    if (emailData?.emails?.length > 0) {
      const emailPromises = emailData.emails.slice(0, 5).map((email: any, index: number) => 
        prisma.contactEmail.create({
          data: {
            contactId: contact.id,
            email: email.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: email.sources
          }
        }).catch(error => {
          console.error('Failed to create email:', email.value, error);
        })
      );

      await Promise.all(emailPromises);
    }

    // Add phones if found
    if (emailData?.phone_numbers?.length > 0) {
      const phonePromises = emailData.phone_numbers.slice(0, 3).map((phone: any, index: number) => 
        prisma.contactPhone.create({
          data: {
            contactId: contact.id,
            phone: phone.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: phone.sources
          }
        }).catch(error => {
          console.error('Failed to create phone:', phone.value, error);
        })
      );

      await Promise.all(phonePromises);
    }

    console.log('Contact created successfully:', contact.name);
    return contact;

  } catch (error) {
    console.error('Failed to create contact:', error);
    throw error;
  }
}
