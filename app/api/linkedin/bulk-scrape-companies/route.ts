import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';
import { linkedInBulkScraperService } from '@/lib/services/linkedin-bulk-scraper-service';
import { revalidateTag } from 'next/cache';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { z } from 'zod';

const bulkScrapeCompaniesSchema = z.object({
  workspaceId: z.string().uuid(),
  linkedinUrls: z.array(z.string().url()).min(1).max(10)
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const parsedInput = bulkScrapeCompaniesSchema.parse(body);

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    console.log('Company bulk scraping request:', {
      workspaceId: parsedInput.workspaceId,
      companyCount: parsedInput.linkedinUrls.length,
      organizationId: session.user.organizationId
    });

    // Step 1: Scrape LinkedIn companies
    const scrapingResult = await linkedInBulkScraperService.scrapeCompanies(parsedInput.linkedinUrls);

    if (!scrapingResult.success) {
      return NextResponse.json({ 
        error: 'Failed to scrape companies',
        details: scrapingResult 
      }, { status: 500 });
    }

    // Step 2: Update LinkedIn profiles in database
    const updatedProfiles = [];
    
    for (const companyResult of scrapingResult.data) {
      try {
        // Update LinkedIn profile with scraped company data
        const updatedProfile = await prisma.linkedInProfile.update({
          where: { linkedinUrl: companyResult.entry },
          data: {
            profileData: companyResult.data,
            scrapedAt: new Date()
          }
        });

        updatedProfiles.push(updatedProfile);

        // Step 3: Background enrichment (email scraping only for companies)
        // Don't await this - let it run in background
        enrichCompanyInBackground(companyResult.data, session.user.organizationId, companyResult.entry)
          .catch(error => {
            console.error('Company background enrichment failed:', error);
          });

      } catch (error) {
        console.error('Failed to update company profile:', companyResult.entry, error);
      }
    }

    // Invalidate contacts cache after bulk operation
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Contacts,
        session.user.organizationId
      )
    );
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.ContactTags,
        session.user.organizationId
      )
    );

    return NextResponse.json({
      success: true,
      creditsUsed: scrapingResult.credits_used,
      companiesScraped: updatedProfiles.length,
      companies: updatedProfiles
    });

  } catch (error) {
    console.error('Company bulk scraping error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Background enrichment for companies: Email scraping only
 */
async function enrichCompanyInBackground(companyData: any, organizationId: string, linkedinUrl: string) {
  try {
    console.log('Company data structure:', {
      hasCompanyData: !!companyData,
      companyDataKeys: companyData ? Object.keys(companyData) : [],
      companyName: companyData?.company_name,
      dataType: typeof companyData,
      linkedinUrl
    });

    console.log('Starting company background enrichment for:', companyData?.company_name || 'Unknown Company');

    let emailData = null;

    // Step 1: Scrape emails if company has valid business website
    if (companyData?.website && isValidBusinessWebsite(companyData.website)) {
      const domain = linkedInBulkScraperService.extractDomain(companyData.website);
      if (domain) {
        try {
          console.log('Scraping emails for company domain:', domain);
          const emailResult = await linkedInBulkScraperService.scrapeEmails(domain);
          
          if (emailResult.status === 'OK' && emailResult.data?.[0]?.emails) {
            // Filter emails to only include those from the target domain
            const filteredEmails = filterEmailsByDomain(emailResult.data[0].emails, domain);
            const filteredPhones = emailResult.data[0].phone_numbers || [];
            
            if (filteredEmails.length > 0) {
              emailData = {
                ...emailResult.data[0],
                emails: filteredEmails,
                phone_numbers: filteredPhones
              };
              console.log(`Filtered emails: ${filteredEmails.length} from ${emailResult.data[0].emails.length} total`);
            }
          } else if (emailResult.status === 'RATE_LIMITED') {
            console.log('Email scraping rate limited, skipping for:', domain);
          } else {
            console.log('Email scraping failed or no emails found for:', domain);
          }
        } catch (error) {
          console.error('Email scraping failed:', error);
        }
      } else {
        console.log('Skipping email scraping - invalid business website:', companyData?.website);
      }
    }

    // Step 2: Create Contact from LinkedIn company data
    await createContactFromLinkedInCompany(companyData, emailData, organizationId, linkedinUrl);

    console.log('Company background enrichment completed for:', companyData.company_name);

  } catch (error) {
    console.error('Company background enrichment error:', error);
  }
}

/**
 * Check if website is a valid business website (not social media)
 */
function isValidBusinessWebsite(website: string): boolean {
  if (!website) return false;
  
  try {
    const url = new URL(website.startsWith('http') ? website : `https://${website}`);
    const hostname = url.hostname.toLowerCase();
    
    // Block social media and non-business domains
    const blockedDomains = [
      'instagram.com', 'facebook.com', 'twitter.com', 'x.com',
      'linkedin.com', 'youtube.com', 'tiktok.com', 'snapchat.com',
      'pinterest.com', 'whatsapp.com', 'telegram.org', 'discord.com',
      'reddit.com', 'tumblr.com', 'vk.com', 'weibo.com', 'wechat.com'
    ];
    
    // Check if hostname contains any blocked domain
    return !blockedDomains.some(blocked => 
      hostname.includes(blocked) || hostname.endsWith(`.${blocked}`)
    );
  } catch {
    return false;
  }
}

/**
 * Filter emails to only include those from the target domain
 */
function filterEmailsByDomain(emails: any[], targetDomain: string): any[] {
  if (!emails || !targetDomain) return [];
  
  return emails.filter(email => {
    if (!email.sources || !Array.isArray(email.sources)) return false;
    
    // Skip obviously fake or generic emails
    const emailValue = email.value?.toLowerCase() || '';
    const fakeEmailPatterns = [
      '<EMAIL>', '<EMAIL>', '<EMAIL>',
      '<EMAIL>', '<EMAIL>', '<EMAIL>'
    ];
    
    if (fakeEmailPatterns.some(pattern => emailValue.includes(pattern))) {
      return false;
    }
    
    // Check if any source URL belongs to the target domain
    return email.sources.some((source: string) => {
      try {
        const sourceUrl = new URL(source);
        const sourceDomain = sourceUrl.hostname.replace('www.', '').toLowerCase();
        const normalizedTargetDomain = targetDomain.replace('www.', '').toLowerCase();
        
        // Must be from the exact target domain (not subdomains or other domains)
        const isFromTargetDomain = sourceDomain === normalizedTargetDomain;
        
        // Additional check: source should not be from social media
        const socialMediaDomains = [
          'instagram.com', 'facebook.com', 'twitter.com', 'linkedin.com',
          'youtube.com', 'tiktok.com', 'snapchat.com', 'pinterest.com'
        ];
        
        const isFromSocialMedia = socialMediaDomains.some(social => 
          sourceDomain.includes(social)
        );
        
        return isFromTargetDomain && !isFromSocialMedia;
      } catch {
        return false;
      }
    });
  });
}

/**
 * Create Contact from LinkedIn company data
 */
async function createContactFromLinkedInCompany(
  companyData: any,
  emailData: any,
  organizationId: string,
  linkedinUrl?: string
) {
  try {
    // Validate company data
    if (!companyData || !companyData.company_name) {
      console.error('Invalid company data for contact creation:', {
        hasCompanyData: !!companyData,
        companyDataKeys: companyData ? Object.keys(companyData) : [],
        linkedinUrl
      });
      return null;
    }

    // Check if contact already exists
    const existingContact = await prisma.contact.findFirst({
      where: {
        organizationId,
        name: companyData.company_name
      }
    });

    if (existingContact) {
      console.log('Company contact already exists:', companyData.company_name);
      return existingContact;
    }

    // Generate smart tags for company
    const smartTags = await createOrConnectCompanyTags(companyData);

    // Create new company contact
    const contact = await prisma.contact.create({
      data: {
        organizationId,
        record: 'COMPANY',
        name: companyData.company_name,
        address: companyData.headquarters?.fullAddress || companyData.locations?.[0]?.fullAddress,
        image: companyData.logo,
        stage: 'LEAD',
        linkedinUrl: linkedinUrl,
        
        // Create activity for LinkedIn company scrape
        activities: {
          create: {
            actionType: 'CREATE',
            actorType: 'SYSTEM',
            actorId: organizationId,
            metadata: {
              source: 'linkedin_company_bulk_scrape',
              companyData,
              emailData,
              scrapedAt: new Date()
            }
          }
        },

        // Add smart tags
        tags: {
          connect: smartTags
        }
      }
    });

    // Add emails if found
    if (emailData?.emails?.length > 0) {
      const emailPromises = emailData.emails.slice(0, 5).map((email: any, index: number) => 
        prisma.contactEmail.create({
          data: {
            contactId: contact.id,
            email: email.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: email.sources
          }
        }).catch(error => {
          console.error('Failed to create company email:', email.value, error);
        })
      );

      await Promise.all(emailPromises);

      // Update contact's primary email
      const primaryEmail = emailData.emails[0]?.value;
      if (primaryEmail) {
        await prisma.contact.update({
          where: { id: contact.id },
          data: { email: primaryEmail }
        });
      }
    }

    // Add phones if found
    if (emailData?.phone_numbers?.length > 0) {
      const phonePromises = emailData.phone_numbers.slice(0, 3).map((phone: any, index: number) => 
        prisma.contactPhone.create({
          data: {
            contactId: contact.id,
            phone: phone.value,
            type: 'work',
            isPrimary: index === 0,
            source: 'website_scrape',
            sources: phone.sources
          }
        }).catch(error => {
          console.error('Failed to create company phone:', phone.value, error);
        })
      );

      await Promise.all(phonePromises);

      // Update contact's primary phone
      const primaryPhone = emailData.phone_numbers[0]?.value;
      if (primaryPhone) {
        await prisma.contact.update({
          where: { id: contact.id },
          data: { phone: primaryPhone }
        });
      }
    }

    console.log('Company contact created successfully:', {
      id: contact.id,
      name: contact.name,
      organizationId: contact.organizationId,
      emailsAdded: emailData?.emails?.length || 0,
      phonesAdded: emailData?.phone_numbers?.length || 0,
      website: companyData?.website || 'N/A'
    });

    // Invalidate contacts cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Contacts,
        organizationId
      )
    );
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.ContactTags,
        organizationId
      )
    );
    
    return contact;

  } catch (error) {
    console.error('Failed to create company contact:', error);
    throw error;
  }
}

/**
 * Create or connect tags for company
 */
async function createOrConnectCompanyTags(companyData: any) {
  const tags = [];
  
  // Add industry tag
  if (companyData.industries) {
    tags.push(companyData.industries.slice(0, 20)); // Max 20 chars
  }
  
  // Add company type tag
  if (companyData.company_name) {
    const name = companyData.company_name.toLowerCase();
    if (name.includes('clinic') || name.includes('klini')) {
      tags.push('Klinik');
    } else if (name.includes('hospital') || name.includes('hastane')) {
      tags.push('Hastane');
    } else if (name.includes('center') || name.includes('merkez')) {
      tags.push('Merkez');
    } else {
      tags.push('Şirket');
    }
  }
  
  // Add location tag if available
  if (companyData.headquarters?.city__State_PostalCode_Country) {
    const location = companyData.headquarters.city__State_PostalCode_Country.split(',')[0]?.trim();
    if (location && location.length <= 20) {
      tags.push(location);
    }
  }
  
  // Limit to 3 tags
  const finalTags = tags.slice(0, 3);
  
  const connectedTags = [];
  for (const tagText of finalTags) {
    try {
      let existingTag = await prisma.contactTag.findUnique({
        where: { text: tagText }
      });
      
      if (!existingTag) {
        existingTag = await prisma.contactTag.create({
          data: { text: tagText }
        });
      }
      
      connectedTags.push({ id: existingTag.id });
    } catch (error) {
      console.error('Failed to create/connect company tag:', tagText, error);
    }
  }
  
  return connectedTags;
}
