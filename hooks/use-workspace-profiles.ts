'use client';

import { useState, useEffect, useCallback } from 'react';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  aiScore: number | null;
  aiReason: string | null;
  aiExtractedData: any;
  aiProcessedAt: Date | null;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

export function useWorkspaceProfiles(workspaceId: string, initialProfiles: WorkspaceProfile[] = []) {
  const [profiles, setProfiles] = useState<WorkspaceProfile[]>(initialProfiles);
  const [isLoading, setIsLoading] = useState(false);

  const refreshProfiles = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/workspaces/${workspaceId}/profiles`);
      if (response.ok) {
        const data = await response.json();
        setProfiles(data);
      }
    } catch (error) {
      console.error('Failed to refresh profiles:', error);
    } finally {
      setIsLoading(false);
    }
  }, [workspaceId]);

  // Auto-refresh every 10 seconds when there are new profiles being processed
  useEffect(() => {
    const interval = setInterval(refreshProfiles, 10000); // 10 seconds
    return () => clearInterval(interval);
  }, [refreshProfiles]);

  return {
    profiles,
    isLoading,
    refreshProfiles
  };
}
