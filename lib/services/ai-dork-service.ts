// AI-powered Google Search Dork Generation Service

import OpenAI from 'openai';

export interface DorkGenerationConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  locations?: string[];
  industry?: string;
  country?: string;
  language?: string;
  includeVariations?: boolean;
  maxDorks?: number;
}

export interface GeneratedDork {
  dork: string;
  description: string;
  targetAudience: string;
  estimatedResults: 'high' | 'medium' | 'low';
}

class AIDorkService {
  private openai: OpenAI;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required');
    }

    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  /**
   * Generate optimized Google search dorks for LinkedIn profiles
   */
  async generateDorks(config: DorkGenerationConfig): Promise<GeneratedDork[]> {
    try {
      const prompt = this.buildDorkPrompt(config);
      
      console.log('AI Dork Generation Request:', {
        userQuery: config.userQuery,
        targetType: config.targetType,
        locations: config.locations,
        industry: config.industry
      });

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in LinkedIn lead generation and Google search optimization. You create highly effective search dorks for finding specific professionals and companies on LinkedIn.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const dorks = this.parseDorksFromResponse(content);
      
      console.log('AI Dork Generation Response:', {
        userQuery: config.userQuery,
        generatedDorks: dorks.length,
        dorks: dorks.map(d => d.dork)
      });

      return dorks;
    } catch (error) {
      console.error('AI Dork Service Error:', error);
      
      // Fallback to template-based dorks if AI fails
      return this.generateFallbackDorks(config);
    }
  }

  /**
   * Build the prompt for AI dork generation
   */
  private buildDorkPrompt(config: DorkGenerationConfig): string {
    const { userQuery, targetType, locations, industry, country, language } = config;

    return `
Generate Google search dorks for finding LinkedIn ${targetType === 'PERSON' ? 'profiles' : 'company pages'}.

User Request: "${userQuery}"
Target Type: ${targetType}
${locations ? `Locations: ${locations.join(', ')}` : ''}
${industry ? `Industry: ${industry}` : ''}
${country ? `Country: ${country}` : ''}
${language ? `Language: ${language}` : ''}

Requirements:
1. Use site:linkedin.com/in for people, site:linkedin.com/company for companies
2. Include both Turkish and English keywords when relevant
3. Add position/title variations for people searches
4. Include location-specific terms
5. Generate 3-5 different dorks with varying specificity
6. Each dork should target different aspects (seniority, company size, etc.)

Format your response as JSON array:
[
  {
    "dork": "site:linkedin.com/in (\"keyword1\" OR \"keyword2\") (\"title1\" OR \"title2\") \"location\"",
    "description": "Brief description of what this dork targets",
    "targetAudience": "Who this dork is best for finding",
    "estimatedResults": "high|medium|low"
  }
]

Focus on Turkish market and business terminology. Include common Turkish business titles and terms.
`;
  }

  /**
   * Parse dorks from AI response
   */
  private parseDorksFromResponse(content: string): GeneratedDork[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed.filter(item => 
            item.dork && 
            item.description && 
            item.targetAudience && 
            item.estimatedResults
          );
        }
      }

      // Fallback: parse line by line
      const lines = content.split('\n');
      const dorks: GeneratedDork[] = [];
      
      for (const line of lines) {
        if (line.includes('site:linkedin.com')) {
          const dork = line.trim().replace(/^[-*]\s*/, '');
          if (dork) {
            dorks.push({
              dork,
              description: 'AI-generated search dork',
              targetAudience: 'General professionals',
              estimatedResults: 'medium'
            });
          }
        }
      }

      return dorks;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return [];
    }
  }

  /**
   * Generate fallback dorks using templates when AI fails
   */
  private generateFallbackDorks(config: DorkGenerationConfig): GeneratedDork[] {
    const { userQuery, targetType, locations } = config;
    const dorks: GeneratedDork[] = [];

    if (targetType === 'PERSON') {
      // Person search templates
      const titles = ['kurucu', 'owner', 'founder', 'CEO', 'müdür', 'director'];
      const titleStr = titles.map(t => `"${t}"`).join(' OR ');

      if (locations && locations.length > 0) {
        for (const location of locations.slice(0, 3)) {
          dorks.push({
            dork: `site:linkedin.com/in "${userQuery}" (${titleStr}) "${location}"`,
            description: `Find ${userQuery} professionals in ${location}`,
            targetAudience: 'Business owners and executives',
            estimatedResults: 'medium'
          });
        }
      } else {
        dorks.push({
          dork: `site:linkedin.com/in "${userQuery}" (${titleStr})`,
          description: `Find ${userQuery} professionals`,
          targetAudience: 'Business owners and executives',
          estimatedResults: 'high'
        });
      }
    } else {
      // Company search templates
      if (locations && locations.length > 0) {
        for (const location of locations.slice(0, 3)) {
          dorks.push({
            dork: `site:linkedin.com/company "${userQuery}" "${location}"`,
            description: `Find ${userQuery} companies in ${location}`,
            targetAudience: 'Companies and organizations',
            estimatedResults: 'medium'
          });
        }
      } else {
        dorks.push({
          dork: `site:linkedin.com/company "${userQuery}"`,
          description: `Find ${userQuery} companies`,
          targetAudience: 'Companies and organizations',
          estimatedResults: 'high'
        });
      }
    }

    return dorks;
  }

  /**
   * Expand keywords using AI
   */
  async expandKeywords(
    baseKeyword: string, 
    industry?: string, 
    language: string = 'tr'
  ): Promise<string[]> {
    try {
      const prompt = `
Expand the keyword "${baseKeyword}" for ${industry || 'general'} industry in ${language} language.

Provide:
1. Direct synonyms
2. Industry-specific terms
3. English equivalents
4. Common abbreviations
5. Local/cultural variations

Return only the keywords, separated by commas:
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 200
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return content
          .split(',')
          .map(k => k.trim())
          .filter(k => k.length > 0)
          .slice(0, 10); // Limit to 10 keywords
      }

      return [baseKeyword];
    } catch (error) {
      console.error('Keyword expansion error:', error);
      return [baseKeyword];
    }
  }
}

// Export singleton instance
export const aiDorkService = new AIDorkService();
