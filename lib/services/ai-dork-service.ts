// AI-powered Google Search Dork Generation Service

import OpenAI from 'openai';

export interface DorkGenerationConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  locations?: string[];
  industry?: string;
  country?: string;
  language?: string;
  includeVariations?: boolean;
  maxDorks?: number;
  previousSearches?: Array<{
    query: string;
    searchDork: string;
    resultsCount: number;
    executedAt: Date;
  }>;
}

export interface GeneratedDork {
  dork: string;
  description: string;
  targetAudience: string;
  estimatedResults: 'high' | 'medium' | 'low';
}

class AIDorkService {
  private openai: OpenAI;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required');
    }

    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  /**
   * Generate optimized Google search dorks for LinkedIn profiles
   */
  async generateDorks(config: DorkGenerationConfig): Promise<GeneratedDork[]> {
    try {
      const prompt = this.buildDorkPrompt(config);
      
      console.log('AI Dork Generation Request:', {
        userQuery: config.userQuery,
        targetType: config.targetType,
        locations: config.locations,
        industry: config.industry
      });

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini', // Much cheaper model - ~$0.15/1M tokens vs $30/1M tokens
        messages: [
          {
            role: 'system',
            content: 'You are an expert in LinkedIn lead generation and Google search optimization. You create SIMPLE and EFFECTIVE search dorks that find MAXIMUM results. Avoid complex queries with too many OR conditions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent, simpler results
        max_tokens: 500 // Reduced tokens for cost efficiency
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const dorks = this.parseDorksFromResponse(content);
      
      console.log('AI Dork Generation Response:', {
        userQuery: config.userQuery,
        generatedDorks: dorks.length,
        dorks: dorks.map(d => d.dork)
      });

      return dorks;
    } catch (error) {
      console.error('AI Dork Service Error:', error);
      
      // Fallback to template-based dorks if AI fails
      return this.generateFallbackDorks(config);
    }
  }

  /**
   * Build the prompt for AI dork generation
   */
  private buildDorkPrompt(config: DorkGenerationConfig): string {
    const { userQuery, targetType, locations, industry, country, language } = config;

    return `
Create LinkedIn search dorks. Be PRECISE and follow the user's EXACT request.

USER REQUEST: "${userQuery}"
LOCATIONS: ${locations ? locations.join(', ') : 'Any'}

RULES:
1. Extract the PROFESSION from the request
2. Add SYNONYMS and RELATED TERMS for the profession (Turkish + English)
3. Extract any ROLE/QUALIFIER mentioned by user
4. DO NOT add qualifiers that user didn't mention
5. Create one dork per location with profession synonyms

EXAMPLES:
"iş yeri sahibi olan diyetisyenler" →
- Profession: "diyetisyen" + synonyms: "dietitian", "beslenme uzmanı", "nutrition specialist"
- Role: "iş yeri sahibi" (owner)
- Dork: site:linkedin.com/in ("diyetisyen" OR "dietitian" OR "beslenme uzmanı") ("sahibi" OR "owner") "location"

"diyetisyen" →
- Profession: "diyetisyen" + synonyms: "dietitian", "beslenme uzmanı"
- Role: NONE
- Dork: site:linkedin.com/in ("diyetisyen" OR "dietitian" OR "beslenme uzmanı") "location"

"deneyimli avukat" →
- Profession: "avukat" + synonyms: "lawyer", "hukukçu"
- Role: "deneyimli"
- Dork: site:linkedin.com/in ("avukat" OR "lawyer" OR "hukukçu") ("deneyimli" OR "experienced") "location"

Return JSON:
[
  {
    "dork": "exact search query",
    "description": "what this finds",
    "targetAudience": "target",
    "estimatedResults": "medium"
  }
]

IMPORTANT: Only use qualifiers that the user ACTUALLY mentioned!
`;
  }

  /**
   * Parse dorks from AI response
   */
  private parseDorksFromResponse(content: string): GeneratedDork[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed.filter(item => 
            item.dork && 
            item.description && 
            item.targetAudience && 
            item.estimatedResults
          );
        }
      }

      // Fallback: parse line by line
      const lines = content.split('\n');
      const dorks: GeneratedDork[] = [];
      
      for (const line of lines) {
        if (line.includes('site:linkedin.com')) {
          const dork = line.trim().replace(/^[-*]\s*/, '');
          if (dork) {
            dorks.push({
              dork,
              description: 'AI-generated search dork',
              targetAudience: 'General professionals',
              estimatedResults: 'medium'
            });
          }
        }
      }

      return dorks;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return [];
    }
  }

  /**
   * Generate fallback dorks using templates when AI fails
   */
  private generateFallbackDorks(config: DorkGenerationConfig): GeneratedDork[] {
    const { userQuery, targetType, locations } = config;
    const dorks: GeneratedDork[] = [];

    // Smart query analysis
    const query = userQuery.toLowerCase();

    // Ownership patterns
    const ownershipPatterns = [
      { pattern: /iş\s*yeri\s*sahibi\s*olan\s*(.+)/i, replacement: '$1', terms: ['sahibi', 'owner', 'kurucu'] },
      { pattern: /işletme\s*sahibi\s*olan\s*(.+)/i, replacement: '$1', terms: ['işletme sahibi', 'owner'] },
      { pattern: /kurucu\s*(.+)/i, replacement: '$1', terms: ['kurucu', 'founder'] },
      { pattern: /(.+)\s*sahibi/i, replacement: '$1', terms: ['sahibi', 'owner'] },
      { pattern: /(.+)\s*kurucu/i, replacement: '$1', terms: ['kurucu', 'founder'] }
    ];

    // Experience patterns
    const experiencePatterns = [
      { pattern: /deneyimli\s*(.+)/i, replacement: '$1', terms: ['deneyimli', 'experienced', 'senior'] },
      { pattern: /uzman\s*(.+)/i, replacement: '$1', terms: ['uzman', 'expert', 'specialist'] },
      { pattern: /senior\s*(.+)/i, replacement: '$1', terms: ['senior', 'deneyimli'] }
    ];

    let profession = userQuery;
    let additionalTerms: string[] = [];

    // Check for ownership patterns
    for (const { pattern, replacement, terms } of ownershipPatterns) {
      const match = userQuery.match(pattern);
      if (match) {
        profession = match[1].trim();
        additionalTerms = terms;
        break;
      }
    }

    // Check for experience patterns if no ownership found
    if (additionalTerms.length === 0) {
      for (const { pattern, replacement, terms } of experiencePatterns) {
        const match = userQuery.match(pattern);
        if (match) {
          profession = match[1].trim();
          additionalTerms = terms;
          break;
        }
      }
    }

    if (targetType === 'PERSON') {
      if (locations && locations.length > 0) {
        for (const location of locations.slice(0, 3)) {
          if (additionalTerms.length > 0) {
            // Targeted search with additional terms
            const termsStr = additionalTerms.map(t => `"${t}"`).join(' OR ');
            dorks.push({
              dork: `site:linkedin.com/in ("${profession}") (${termsStr}) "${location}"`,
              description: `Find ${profession} with specific qualifications in ${location}`,
              targetAudience: 'Qualified professionals',
              estimatedResults: 'medium'
            });
          }

          // Always add a general search too
          dorks.push({
            dork: `site:linkedin.com/in "${profession}" "${location}"`,
            description: `Find ${profession} professionals in ${location}`,
            targetAudience: 'All professionals',
            estimatedResults: 'high'
          });
        }
      } else {
        if (additionalTerms.length > 0) {
          const termsStr = additionalTerms.map(t => `"${t}"`).join(' OR ');
          dorks.push({
            dork: `site:linkedin.com/in ("${profession}") (${termsStr})`,
            description: `Find ${profession} with specific qualifications`,
            targetAudience: 'Qualified professionals',
            estimatedResults: 'medium'
          });
        }

        dorks.push({
          dork: `site:linkedin.com/in "${profession}"`,
          description: `Find ${profession} professionals`,
          targetAudience: 'All professionals',
          estimatedResults: 'high'
        });
      }
    } else {
      // Company searches
      if (locations && locations.length > 0) {
        for (const location of locations.slice(0, 3)) {
          dorks.push({
            dork: `site:linkedin.com/company "${profession}" "${location}"`,
            description: `Find ${profession} companies in ${location}`,
            targetAudience: 'Companies',
            estimatedResults: 'high'
          });
        }
      } else {
        dorks.push({
          dork: `site:linkedin.com/company "${profession}"`,
          description: `Find ${profession} companies`,
          targetAudience: 'Companies',
          estimatedResults: 'high'
        });
      }
    }

    return dorks;
  }

  /**
   * Expand keywords using AI
   */
  async expandKeywords(
    baseKeyword: string, 
    industry?: string, 
    language: string = 'tr'
  ): Promise<string[]> {
    try {
      const prompt = `
Expand the keyword "${baseKeyword}" for ${industry || 'general'} industry in ${language} language.

Provide:
1. Direct synonyms
2. Industry-specific terms
3. English equivalents
4. Common abbreviations
5. Local/cultural variations

Return only the keywords, separated by commas:
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini', // Much cheaper for keyword expansion
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 100 // Reduced for cost efficiency
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return content
          .split(',')
          .map(k => k.trim())
          .filter(k => k.length > 0)
          .slice(0, 10); // Limit to 10 keywords
      }

      return [baseKeyword];
    } catch (error) {
      console.error('Keyword expansion error:', error);
      return [baseKeyword];
    }
  }
}

// Export singleton instance
export const aiDorkService = new AIDorkService();
