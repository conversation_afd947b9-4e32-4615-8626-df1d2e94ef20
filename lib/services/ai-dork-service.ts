// AI-powered Google Search Dork Generation Service

import OpenAI from 'openai';

export interface DorkGenerationConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  locations?: string[];
  industry?: string;
  country?: string;
  language?: string;
  includeVariations?: boolean;
  maxDorks?: number;
}

export interface GeneratedDork {
  dork: string;
  description: string;
  targetAudience: string;
  estimatedResults: 'high' | 'medium' | 'low';
}

class AIDorkService {
  private openai: OpenAI;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required');
    }

    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  /**
   * Generate optimized Google search dorks for LinkedIn profiles
   */
  async generateDorks(config: DorkGenerationConfig): Promise<GeneratedDork[]> {
    try {
      const prompt = this.buildDorkPrompt(config);
      
      console.log('AI Dork Generation Request:', {
        userQuery: config.userQuery,
        targetType: config.targetType,
        locations: config.locations,
        industry: config.industry
      });

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini', // Much cheaper model - ~$0.15/1M tokens vs $30/1M tokens
        messages: [
          {
            role: 'system',
            content: 'You are an expert in LinkedIn lead generation and Google search optimization. You create SIMPLE and EFFECTIVE search dorks that find MAXIMUM results. Avoid complex queries with too many OR conditions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent, simpler results
        max_tokens: 500 // Reduced tokens for cost efficiency
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const dorks = this.parseDorksFromResponse(content);
      
      console.log('AI Dork Generation Response:', {
        userQuery: config.userQuery,
        generatedDorks: dorks.length,
        dorks: dorks.map(d => d.dork)
      });

      return dorks;
    } catch (error) {
      console.error('AI Dork Service Error:', error);
      
      // Fallback to template-based dorks if AI fails
      return this.generateFallbackDorks(config);
    }
  }

  /**
   * Build the prompt for AI dork generation
   */
  private buildDorkPrompt(config: DorkGenerationConfig): string {
    const { userQuery, targetType, locations, industry, country, language } = config;

    return `
Generate SIMPLE and EFFECTIVE Google search dorks for finding LinkedIn ${targetType === 'PERSON' ? 'profiles' : 'company pages'}.

User Request: "${userQuery}"
Target Type: ${targetType}
${locations ? `Locations: ${locations.join(', ')}` : ''}

IMPORTANT RULES:
1. Keep dorks SIMPLE - avoid too many OR conditions
2. Use site:linkedin.com/in for people, site:linkedin.com/company for companies
3. Generate 3-5 different dorks with DIFFERENT approaches
4. Start with broader searches, then get more specific
5. Each location should have separate dorks
6. Don't combine too many keywords in one dork

EXAMPLES OF GOOD DORKS:
- site:linkedin.com/in "diyetisyen" "İstanbul"
- site:linkedin.com/in "dietitian" "kurucu" "Ankara"
- site:linkedin.com/in "beslenme uzmanı" "İzmir"

Format your response as JSON array:
[
  {
    "dork": "site:linkedin.com/in \"keyword\" \"location\"",
    "description": "Brief description",
    "targetAudience": "Target audience",
    "estimatedResults": "high|medium|low"
  }
]

Generate SIMPLE dorks that will find MORE results, not fewer!
`;
  }

  /**
   * Parse dorks from AI response
   */
  private parseDorksFromResponse(content: string): GeneratedDork[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed.filter(item => 
            item.dork && 
            item.description && 
            item.targetAudience && 
            item.estimatedResults
          );
        }
      }

      // Fallback: parse line by line
      const lines = content.split('\n');
      const dorks: GeneratedDork[] = [];
      
      for (const line of lines) {
        if (line.includes('site:linkedin.com')) {
          const dork = line.trim().replace(/^[-*]\s*/, '');
          if (dork) {
            dorks.push({
              dork,
              description: 'AI-generated search dork',
              targetAudience: 'General professionals',
              estimatedResults: 'medium'
            });
          }
        }
      }

      return dorks;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return [];
    }
  }

  /**
   * Generate fallback dorks using templates when AI fails
   */
  private generateFallbackDorks(config: DorkGenerationConfig): GeneratedDork[] {
    const { userQuery, targetType, locations } = config;
    const dorks: GeneratedDork[] = [];

    if (targetType === 'PERSON') {
      // Simple person search templates
      if (locations && locations.length > 0) {
        // Basic search for each location
        for (const location of locations.slice(0, 3)) {
          dorks.push({
            dork: `site:linkedin.com/in "${userQuery}" "${location}"`,
            description: `Find ${userQuery} in ${location}`,
            targetAudience: 'Professionals',
            estimatedResults: 'high'
          });
        }

        // Add some title variations for first location only
        const firstLocation = locations[0];
        dorks.push({
          dork: `site:linkedin.com/in "${userQuery}" "kurucu" "${firstLocation}"`,
          description: `Find ${userQuery} founders in ${firstLocation}`,
          targetAudience: 'Business owners',
          estimatedResults: 'medium'
        });

        dorks.push({
          dork: `site:linkedin.com/in "${userQuery}" "owner" "${firstLocation}"`,
          description: `Find ${userQuery} owners in ${firstLocation}`,
          targetAudience: 'Business owners',
          estimatedResults: 'medium'
        });
      } else {
        dorks.push({
          dork: `site:linkedin.com/in "${userQuery}"`,
          description: `Find ${userQuery} professionals`,
          targetAudience: 'All professionals',
          estimatedResults: 'high'
        });
      }
    } else {
      // Company search templates
      if (locations && locations.length > 0) {
        for (const location of locations.slice(0, 3)) {
          dorks.push({
            dork: `site:linkedin.com/company "${userQuery}" "${location}"`,
            description: `Find ${userQuery} companies in ${location}`,
            targetAudience: 'Companies and organizations',
            estimatedResults: 'high'
          });
        }
      } else {
        dorks.push({
          dork: `site:linkedin.com/company "${userQuery}"`,
          description: `Find ${userQuery} companies`,
          targetAudience: 'Companies and organizations',
          estimatedResults: 'high'
        });
      }
    }

    return dorks;
  }

  /**
   * Expand keywords using AI
   */
  async expandKeywords(
    baseKeyword: string, 
    industry?: string, 
    language: string = 'tr'
  ): Promise<string[]> {
    try {
      const prompt = `
Expand the keyword "${baseKeyword}" for ${industry || 'general'} industry in ${language} language.

Provide:
1. Direct synonyms
2. Industry-specific terms
3. English equivalents
4. Common abbreviations
5. Local/cultural variations

Return only the keywords, separated by commas:
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 200
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return content
          .split(',')
          .map(k => k.trim())
          .filter(k => k.length > 0)
          .slice(0, 10); // Limit to 10 keywords
      }

      return [baseKeyword];
    } catch (error) {
      console.error('Keyword expansion error:', error);
      return [baseKeyword];
    }
  }
}

// Export singleton instance
export const aiDorkService = new AIDorkService();
