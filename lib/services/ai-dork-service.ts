// AI-powered Google Search Dork Generation Service

import OpenAI from 'openai';

export interface DorkGenerationConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  locations?: string[];
  industry?: string;
  country?: string;
  language?: string;
  previousSearches?: Array<{
    query: string;
    searchDork: string;
    resultsCount: number;
    executedAt: Date;
  }>;
}

export interface GeneratedDork {
  dork: string;
  description: string;
  targetAudience: string;
  estimatedResults: 'high' | 'medium' | 'low';
}

class AIDorkService {
  private openai: OpenAI;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required');
    }

    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  /**
   * Generate optimized Google search dorks for LinkedIn profiles
   */
  async generateDorks(config: DorkGenerationConfig): Promise<GeneratedDork[]> {
    try {
      const prompt = this.buildDorkPrompt(config);
      
      console.log('AI Dork Generation Request:', {
        userQuery: config.userQuery,
        targetType: config.targetType,
        locations: config.locations,
        industry: config.industry
      });

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini', // Much cheaper model - ~$0.15/1M tokens vs $30/1M tokens
        messages: [
          {
            role: 'system',
            content: 'You are an expert in LinkedIn lead generation and Google search optimization. You create SIMPLE and EFFECTIVE search dorks that find MAXIMUM results. Avoid complex queries with too many OR conditions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent, simpler results
        max_tokens: 800 // Increased to prevent truncation of dorks
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const dorks = this.parseDorksFromResponse(content);
      
      console.log('AI Dork Generation Response:', {
        userQuery: config.userQuery,
        generatedDorks: dorks.length,
        dorks: dorks.map(d => d.dork),
        rawResponse: content.substring(0, 200) + '...' // Log first 200 chars for debugging
      });

      return dorks;
    } catch (error) {
      console.error('AI Dork Service Error:', error);
      
      // Fallback to template-based dorks if AI fails
      return this.generateFallbackDorks(config);
    }
  }

  /**
   * Build the prompt for AI dork generation
   */
  private buildDorkPrompt(config: DorkGenerationConfig): string {
    const { userQuery, targetType, locations, industry, country, language, maxDorks, previousSearches } = config;

    let previousSearchContext = '';
    if (previousSearches && previousSearches.length > 0) {
      previousSearchContext = `
PREVIOUS SEARCHES IN THIS WORKSPACE:
${previousSearches.map(search =>
  `- Query: "${search.query}" → Dork: "${search.searchDork}" → Results: ${search.resultsCount}`
).join('\n')}

ANALYSIS REQUIRED:
- If current query is similar to previous ones, try DIFFERENT approaches
- If previous searches got 0 results, avoid similar patterns
- Create VARIATIONS that haven't been tried before
- Learn from what worked (high results) and what didn't (0 results)
`;
    }

    return `
You are an expert LinkedIn search specialist. Create SMART and DETAILED search dorks.

USER REQUEST: "${userQuery}"
LOCATIONS: ${locations ? locations.slice(0, 3).join(', ') : 'Any'}
MAX DORKS: 6 (NEVER exceed this)
${previousSearchContext}

SMART ANALYSIS RULES:
1. Extract PROFESSION and add relevant synonyms (Turkish + English)
2. Extract ROLE/POSITION and add appropriate variations
3. Create detailed OR combinations for better results
4. Use 50 results per search (fixed)
5. Avoid duplicate patterns from previous searches

PROFESSION EXPANSION:
- "diyetisyen" → "diyetisyen" OR "dietitian" OR "beslenme uzmanı" OR "nutrition specialist"
- "fizyoterapist" → "fizyoterapist" OR "physiotherapist" OR "fizik tedavi uzmanı"
- "avukat" → "avukat" OR "lawyer" OR "hukukçu"
- "mimar" → "mimar" OR "architect"

ROLE EXPANSION (only for general terms):
- "iş yeri sahibi" → "sahibi" OR "owner" OR "kurucu" OR "founder" OR "işletme sahibi"
- "kurucu" → "kurucu" OR "founder" OR "co-founder"
- "müdür" → "müdür" OR "director" OR "manager"

SPECIFIC TITLES (don't expand):
- "CEO" → ONLY "CEO" (don't add CTO, CMO, etc.)
- "CTO" → ONLY "CTO"
- "CFO" → ONLY "CFO"

EXAMPLES:
"iş yeri sahibi olan diyetisyenler" →
site:linkedin.com/in ("diyetisyen" OR "dietitian" OR "beslenme uzmanı") ("sahibi" OR "owner" OR "kurucu" OR "founder") "İstanbul"

"CEO konumundaki dijital pazarlama uzmanları" →
site:linkedin.com/in ("dijital pazarlama" OR "digital marketing" OR "pazarlama") "CEO" "İstanbul"

VARIATION STRATEGY:
- Create different combinations to avoid previous patterns
- Use similar but different phrasings
- Mix Turkish and English terms strategically

Return JSON array (max 6 dorks):
[
  {
    "dork": "site:linkedin.com/in (\"term1\" OR \"term2\") (\"role1\" OR \"role2\") \"location\"",
    "description": "what this finds",
    "targetAudience": "target",
    "estimatedResults": "medium"
  }
]

CREATE DETAILED OR COMBINATIONS FOR MAXIMUM RESULTS!
`;
  }

  /**
   * Check if a dork is complete (not truncated)
   */
  private isDorkComplete(dork: string): boolean {
    // A complete dork should have:
    // 1. site:linkedin.com/in
    // 2. At least one quoted term
    // 3. Proper closing quotes
    // 4. Not end abruptly

    if (!dork.includes('site:linkedin.com/in')) return false;

    // Count opening and closing quotes - should be even
    const quoteCount = (dork.match(/"/g) || []).length;
    if (quoteCount % 2 !== 0) return false;

    // Should not end with incomplete patterns
    const incompletePatterns = [
      /\s+OR\s*$/i,
      /\s+AND\s*$/i,
      /\(\s*$/,
      /"\s*$/,
      /\s+$/
    ];

    return !incompletePatterns.some(pattern => pattern.test(dork));
  }

  /**
   * Parse dorks from AI response
   */
  private parseDorksFromResponse(content: string): GeneratedDork[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed.filter(item => 
            item.dork && 
            item.description && 
            item.targetAudience && 
            item.estimatedResults
          );
        }
      }

      // Fallback: parse line by line and extract dork values
      const lines = content.split('\n');
      const dorks: GeneratedDork[] = [];

      for (const line of lines) {
        if (line.includes('site:linkedin.com')) {
          // Extract dork from various formats
          let dork = line.trim();

          // Remove JSON formatting
          dork = dork.replace(/^"dork":\s*"/, '').replace(/",?$/, '');
          dork = dork.replace(/\\"/g, '"'); // Unescape quotes
          dork = dork.replace(/^[-*]\s*/, ''); // Remove bullet points

          // Check if dork is complete (not truncated)
          if (dork && dork.includes('site:linkedin.com') && this.isDorkComplete(dork)) {
            dorks.push({
              dork,
              description: 'AI-generated search dork',
              targetAudience: 'General professionals',
              estimatedResults: 'medium'
            });
          } else if (dork && dork.includes('site:linkedin.com')) {
            console.warn('Incomplete dork detected and skipped:', dork);
          }
        }
      }

      return dorks;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return [];
    }
  }

  /**
   * Generate fallback dorks using templates when AI fails
   */
  private generateFallbackDorks(config: DorkGenerationConfig): GeneratedDork[] {
    const { userQuery, targetType, locations } = config;

    // Simple fallback - just use the query as-is and let AI figure it out
    // This fallback should rarely be used since AI should handle most cases
    const dorks: GeneratedDork[] = [];

    if (locations && locations.length > 0) {
      for (const location of locations.slice(0, 3)) {
        dorks.push({
          dork: `site:linkedin.com/in "${userQuery}" "${location}"`,
          description: `Find ${userQuery} in ${location}`,
          targetAudience: 'Professionals',
          estimatedResults: 'medium'
        });
      }
    } else {
      dorks.push({
        dork: `site:linkedin.com/in "${userQuery}"`,
        description: `Find ${userQuery}`,
        targetAudience: 'Professionals',
        estimatedResults: 'medium'
      });
    }

    return dorks;


  }

  /**
   * Expand keywords using AI
   */
  async expandKeywords(
    baseKeyword: string, 
    industry?: string, 
    language: string = 'tr'
  ): Promise<string[]> {
    try {
      const prompt = `
Expand the keyword "${baseKeyword}" for ${industry || 'general'} industry in ${language} language.

Provide:
1. Direct synonyms
2. Industry-specific terms
3. English equivalents
4. Common abbreviations
5. Local/cultural variations

Return only the keywords, separated by commas:
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini', // Much cheaper for keyword expansion
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 100 // Reduced for cost efficiency
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return content
          .split(',')
          .map(k => k.trim())
          .filter(k => k.length > 0)
          .slice(0, 10); // Limit to 10 keywords
      }

      return [baseKeyword];
    } catch (error) {
      console.error('Keyword expansion error:', error);
      return [baseKeyword];
    }
  }
}

// Export singleton instance
export const aiDorkService = new AIDorkService();
