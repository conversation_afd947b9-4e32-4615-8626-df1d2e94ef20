interface LinkedInBulkScraperResponse {
  success: boolean;
  status: number;
  credits_used: number;
  data: Array<{
    entry: string;
    data: {
      firstName: string;
      lastName: string;
      fullName: string;
      headline: string;
      connections: number;
      followers: number;
      addressWithCountry: string;
      profilePic: string;
      about?: string;
      experiences: Array<{
        companyId?: string;
        companyUrn?: string;
        title: string;
        subtitle: string;
        caption: string;
        metadata?: string;
      }>;
      educations: Array<{
        companyId?: string;
        title: string;
        subtitle: string;
        caption: string;
      }>;
      skills: Array<{
        title: string;
      }>;
      languages: Array<{
        title: string;
        caption?: string;
      }>;
    };
  }>;
}

interface CompanyScraperResponse {
  success: boolean;
  status: number;
  data: {
    company_name: string;
    website?: string;
    domain?: string;
    headquarters?: {
      fullAddress: string;
      city__State_PostalCode_Country: string;
    };
    description?: string;
    employees?: Array<{
      name: string;
      title: string;
      link: string;
    }>;
    industries?: string;
    founded_in?: number;
    followers?: number;
  };
}

interface EmailScraperResponse {
  status: string;
  data: Array<{
    domain: string;
    emails: Array<{
      value: string;
      sources: string[];
    }>;
    phone_numbers: Array<{
      value: string;
      sources: string[];
    }>;
  }>;
}

class LinkedInBulkScraperService {
  private readonly apiKey: string;
  private readonly singleApiKey: string;
  private readonly emailApiKey: string;
  private readonly bulkScraperUrl = 'https://linkedin-bulk-data-scraper.p.rapidapi.com/profiles';
  private readonly bulkCompanyScraperUrl = 'https://linkedin-bulk-data-scraper.p.rapidapi.com/companies';
  private readonly companyScraperUrl = 'https://linkedin-data-scraper.p.rapidapi.com/company';
  private readonly emailScraperUrl = 'https://website-contacts-scraper.p.rapidapi.com/scrape-contacts';

  constructor() {
    this.apiKey = process.env.LINKEDIN_BULK_DATA_SCRAPER_API_KEY!;
    this.singleApiKey = process.env.LINKEDIN_SINLGE_DATA_SCRAPER_API_KEY!;
    this.emailApiKey = process.env.EMAIL_SCRAPER_API_KEY!;

    if (!this.apiKey) {
      throw new Error('LINKEDIN_BULK_DATA_SCRAPER_API_KEY is required');
    }
    if (!this.singleApiKey) {
      throw new Error('LINKEDIN_SINLGE_DATA_SCRAPER_API_KEY is required');
    }
    if (!this.emailApiKey) {
      throw new Error('EMAIL_SCRAPER_API_KEY is required');
    }
  }

  /**
   * Scrape multiple LinkedIn profiles (max 10)
   */
  async scrapeProfiles(linkedinUrls: string[]): Promise<LinkedInBulkScraperResponse> {
    if (linkedinUrls.length === 0) {
      throw new Error('At least one LinkedIn URL is required');
    }

    if (linkedinUrls.length > 10) {
      throw new Error('Maximum 10 profiles can be scraped at once');
    }

    console.log('LinkedIn Bulk Scraper Request:', {
      url: this.bulkScraperUrl,
      profileCount: linkedinUrls.length,
      profiles: linkedinUrls
    });

    try {
      const response = await fetch(this.bulkScraperUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'linkedin-bulk-data-scraper.p.rapidapi.com',
          'x-rapidapi-key': this.apiKey
        },
        body: JSON.stringify({
          links: linkedinUrls
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('LinkedIn Bulk Scraper Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`LinkedIn Bulk Scraper error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      console.log('LinkedIn Bulk Scraper Response:', {
        success: data.success,
        creditsUsed: data.credits_used,
        profilesScraped: data.data?.length || 0
      });

      return data;

    } catch (error) {
      console.error('LinkedIn Bulk Scraper Service Error:', error);
      throw error;
    }
  }

  /**
   * Scrape multiple LinkedIn companies (max 10)
   */
  async scrapeCompanies(linkedinUrls: string[]): Promise<LinkedInBulkScraperResponse> {
    if (linkedinUrls.length === 0) {
      throw new Error('At least one LinkedIn company URL is required');
    }

    if (linkedinUrls.length > 10) {
      throw new Error('Maximum 10 companies can be scraped at once');
    }

    console.log('LinkedIn Company Bulk Scraper Request:', {
      url: this.bulkCompanyScraperUrl,
      companyCount: linkedinUrls.length,
      companies: linkedinUrls
    });

    try {
      const response = await fetch(this.bulkCompanyScraperUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'linkedin-bulk-data-scraper.p.rapidapi.com',
          'x-rapidapi-key': this.apiKey,
          'x-rapidapi-user': 'usama'
        },
        body: JSON.stringify({
          links: linkedinUrls
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('LinkedIn Company Bulk Scraper Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`LinkedIn Company Bulk Scraper error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log('LinkedIn Company Bulk Scraper Response:', {
        success: data.success,
        creditsUsed: data.credits_used,
        companiesScraped: data.data?.length || 0
      });

      return data;

    } catch (error) {
      console.error('LinkedIn Company Bulk Scraper Service Error:', error);
      throw error;
    }
  }

  /**
   * Scrape company data by company ID
   */
  async scrapeCompany(companyId: string): Promise<CompanyScraperResponse> {
    console.log('Company Scraper Request:', {
      url: this.companyScraperUrl,
      companyId
    });

    try {
      const response = await fetch(this.companyScraperUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-rapidapi-host': 'linkedin-data-scraper.p.rapidapi.com',
          'x-rapidapi-key': this.singleApiKey
        },
        body: JSON.stringify({
          link: `https://www.linkedin.com/company/${companyId}`
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Company Scraper Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        return { success: false, status: response.status, data: null };
      }

      const data = await response.json();

      console.log('Company Scraper Response:', {
        success: data.success,
        companyName: data.data?.company_name,
        website: data.data?.website
      });

      return data;

    } catch (error) {
      console.error('Company Scraper Service Error:', error);
      return { success: false, status: 500, data: null };
    }
  }

  /**
   * Scrape emails from website domain
   */
  async scrapeEmails(domain: string): Promise<EmailScraperResponse> {
    console.log('Email Scraper Request:', {
      url: this.emailScraperUrl,
      domain
    });

    try {
      const params = new URLSearchParams({
        query: domain,
        match_email_domain: 'false',
        external_matching: 'false'
      });

      const response = await fetch(`${this.emailScraperUrl}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'x-rapidapi-host': 'website-contacts-scraper.p.rapidapi.com',
          'x-rapidapi-key': this.emailApiKey
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Email Scraper Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        // Handle rate limit gracefully
        if (response.status === 429) {
          console.log('Rate limit hit, skipping email scraping for this domain');
          return { status: 'RATE_LIMITED', data: [] };
        }

        return { status: 'ERROR', data: [] };
      }

      const data = await response.json();
      
      console.log('Email Scraper Response:', {
        status: data.status,
        domain,
        emailsFound: data.data?.[0]?.emails?.length || 0,
        phonesFound: data.data?.[0]?.phone_numbers?.length || 0
      });

      return data;

    } catch (error) {
      console.error('Email Scraper Service Error:', error);
      throw error;
    }
  }

  /**
   * Extract company ID from LinkedIn experience
   */
  extractCompanyId(experience: any): string | null {
    return experience.companyId || 
           experience.companyUrn?.split(':').pop() || 
           null;
  }

  /**
   * Extract domain from website URL
   */
  extractDomain(website: string): string | null {
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      return url.hostname.replace('www.', '');
    } catch {
      return null;
    }
  }
}

// Export singleton instance
export const linkedInBulkScraperService = new LinkedInBulkScraperService();
export type { LinkedInBulkScraperResponse, CompanyScraperResponse, EmailScraperResponse };
