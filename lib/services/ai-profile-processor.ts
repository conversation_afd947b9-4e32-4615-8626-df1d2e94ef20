import OpenAI from 'openai';

interface RawSearchResult {
  title: string;
  url: string;
  description?: string;
  snippet?: string;
}

interface ProcessedProfile {
  title: string;
  description: string;
  linkedinUrl: string;
  extractedInfo: {
    name?: string;
    position?: string;
    company?: string;
    location?: string;
    industry?: string;
    isRelevant: boolean;
    relevanceScore: number;
    relevanceReason: string;
  };
}

interface ProcessingConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  industry?: string;
  locations?: string[];
}

class AIProfileProcessor {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  /**
   * Process raw Google search results with AI
   */
  async processSearchResults(
    rawResults: RawSearchResult[],
    config: ProcessingConfig
  ): Promise<ProcessedProfile[]> {
    if (rawResults.length === 0) return [];

    try {
      const prompt = this.buildProcessingPrompt(rawResults, config);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing LinkedIn profiles and extracting relevant information. You understand business contexts and can determine if a profile matches user requirements.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return this.parseProcessedResults(content, rawResults);
      }

      return this.fallbackProcessing(rawResults, config);
    } catch (error) {
      console.error('AI processing error:', error);
      return this.fallbackProcessing(rawResults, config);
    }
  }

  /**
   * Build AI processing prompt
   */
  private buildProcessingPrompt(results: RawSearchResult[], config: ProcessingConfig): string {
    const { userQuery, targetType, industry, locations } = config;

    const resultsText = results.map((result, index) => 
      `${index + 1}. Title: "${result.title}"\n   URL: ${result.url}\n   Description: "${result.description || result.snippet || 'No description'}"`
    ).join('\n\n');

    return `
Analyze these LinkedIn search results and extract relevant information.

USER REQUEST: "${userQuery}"
TARGET TYPE: ${targetType}
INDUSTRY: ${industry || 'Any'}
LOCATIONS: ${locations?.join(', ') || 'Any'}

SEARCH RESULTS:
${resultsText}

YOUR TASK:
1. For each result, extract:
   - Person's name (from title)
   - Current position/title
   - Company name
   - Location (if mentioned)
   - Industry (if mentioned)

2. Determine relevance:
   - Does this profile match what the user is looking for?
   - Rate relevance 1-10 (10 = perfect match)
   - Explain why it's relevant or not

3. Clean and format the information

Return JSON array:
[
  {
    "index": 1,
    "name": "extracted name",
    "position": "extracted position",
    "company": "extracted company",
    "location": "extracted location",
    "industry": "extracted industry",
    "isRelevant": true/false,
    "relevanceScore": 1-10,
    "relevanceReason": "why this profile matches or doesn't match user request",
    "cleanTitle": "cleaned profile title",
    "cleanDescription": "cleaned and enhanced description"
  }
]

Focus on accuracy and relevance to user's request.
`;
  }

  /**
   * Parse AI processed results
   */
  private parseProcessedResults(content: string, rawResults: RawSearchResult[]): ProcessedProfile[] {
    try {
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed
            .filter(item => item.index && item.index <= rawResults.length)
            .map(item => {
              const rawResult = rawResults[item.index - 1];
              return {
                title: item.cleanTitle || rawResult.title,
                description: item.cleanDescription || rawResult.description || rawResult.snippet || '',
                linkedinUrl: rawResult.url,
                extractedInfo: {
                  name: item.name,
                  position: item.position,
                  company: item.company,
                  location: item.location,
                  industry: item.industry,
                  isRelevant: item.isRelevant,
                  relevanceScore: item.relevanceScore || 5,
                  relevanceReason: item.relevanceReason || 'No reason provided'
                }
              };
            });
        }
      }

      return this.fallbackProcessing(rawResults, { userQuery: '', targetType: 'PERSON' });
    } catch (error) {
      console.error('Error parsing AI processed results:', error);
      return this.fallbackProcessing(rawResults, { userQuery: '', targetType: 'PERSON' });
    }
  }

  /**
   * Fallback processing when AI fails
   */
  private fallbackProcessing(rawResults: RawSearchResult[], config: ProcessingConfig): ProcessedProfile[] {
    return rawResults.map(result => {
      // Basic title cleaning
      const cleanTitle = result.title
        .replace(/ - LinkedIn.*$/, '')
        .replace(/\s+/g, ' ')
        .trim();

      // Basic name extraction (first part of title)
      const nameParts = cleanTitle.split(/[-|,]/);
      const name = nameParts[0]?.trim() || 'Unknown';

      // Basic position extraction
      const position = nameParts[1]?.trim() || '';

      return {
        title: cleanTitle,
        description: result.description || result.snippet || '',
        linkedinUrl: result.url,
        extractedInfo: {
          name,
          position,
          company: '',
          location: '',
          industry: '',
          isRelevant: true, // Assume relevant in fallback
          relevanceScore: 7,
          relevanceReason: 'Basic processing - manual review recommended'
        }
      };
    });
  }

  /**
   * Filter results by relevance
   */
  filterByRelevance(profiles: ProcessedProfile[], minScore: number = 6): ProcessedProfile[] {
    return profiles.filter(profile => 
      profile.extractedInfo.isRelevant && 
      profile.extractedInfo.relevanceScore >= minScore
    );
  }

  /**
   * Sort by relevance score
   */
  sortByRelevance(profiles: ProcessedProfile[]): ProcessedProfile[] {
    return profiles.sort((a, b) => 
      b.extractedInfo.relevanceScore - a.extractedInfo.relevanceScore
    );
  }
}

// Export singleton instance
export const aiProfileProcessor = new AIProfileProcessor();
export type { ProcessedProfile, ProcessingConfig };
