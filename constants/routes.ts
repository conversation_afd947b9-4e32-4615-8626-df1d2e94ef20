export enum Routes {
  Root = '/',
  Contact = '/contact',
  Roadmap = 'https://achromatic.canny.io',
  Docs = '/docs',
  Pricing = '/pricing',
  Blog = '/blog',
  Story = '/story',
  Careers = '/careers',
  TermsOfUse = '/terms-of-use',
  PrivacyPolicy = '/privacy-policy',
  CookiePolicy = '/cookie-policy',

  Auth = '/auth',
  Login = '/auth/login',
  Logout = '/auth/logout',
  SignUp = '/auth/signup',
  AuthError = '/auth/error',
  Totp = '/auth/totp',
  RecoveryCode = '/auth/recovery-code',
  ChangeEmail = '/auth/change-email',
  ChangeEmailRequest = '/auth/change-email/request',
  ChangeEmailInvalid = '/auth/change-email/invalid',
  ChangeEmailExpired = '/auth/change-email/expired',
  ForgotPassword = '/auth/forgot-password',
  ForgotPasswordSuccess = '/auth/forgot-password/success',
  ResetPassword = '/auth/reset-password',
  ResetPasswordRequest = '/auth/reset-password/request',
  ResetPasswordExpired = '/auth/reset-password/expired',
  ResetPasswordSuccess = '/auth/reset-password/success',
  VerifyEmail = '/auth/verify-email',
  VerifyEmailRequest = '/auth/verify-email/request',
  VerifyEmailExpired = '/auth/verify-email/expired',
  VerifyEmailSuccess = '/auth/verify-email/success',

  Dashboard = '/dashboard',
  Home = '/dashboard/home',
  Contacts = '/dashboard/contacts',
  Settings = '/dashboard/settings',
  Account = '/dashboard/settings/account',
  Profile = '/dashboard/settings/account/profile',
  Security = '/dashboard/settings/account/security',
  Notifications = '/dashboard/settings/account/notifications',
  Organization = '/dashboard/settings/organization',
  OrganizationInformation = '/dashboard/settings/organization/information',
  Members = '/dashboard/settings/organization/members',
  Billing = '/dashboard/settings/organization/billing',
  Developers = '/dashboard/settings/organization/developers',

  Invitations = '/invitations',
  InvitationRequest = '/invitations/request',
  InvitationAlreadyAccepted = '/invitations/already-accepted',
  InvitationRevoked = '/invitations/revoked',
  InvitationLogOutToAccept = '/invitations/log-out-to-accept',

  Onboarding = '/onboarding',

  SuperAdmin = '/super-admin',
  SuperAdminOrganizations = '/super-admin/organizations',
  SuperAdminUsers = '/super-admin/users',
  SuperAdminBilling = '/super-admin/billing',
  SuperAdminSystem = '/super-admin/system'
}
