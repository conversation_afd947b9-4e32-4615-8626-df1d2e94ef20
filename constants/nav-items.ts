import {
  BarChart3Icon,
  BellIcon,
  BuildingIcon,
  CodeIcon,
  CreditCardIcon,
  DatabaseIcon,
  GlobeIcon,
  HomeIcon,
  LockKeyholeIcon,
  MailIcon,
  SettingsIcon,
  ShieldIcon,
  StoreIcon,
  UserIcon,
  UserPlus2Icon,
  UsersIcon
} from 'lucide-react';

import { Routes } from '@/constants/routes';
import type { NavItem } from '@/types/nav-item';

export const mainNavItems: NavItem[] = [
  {
    title: 'Home',
    href: Routes.Home,
    icon: HomeIcon
  },
  {
    title: 'Contacts',
    href: Routes.Contacts,
    icon: UsersIcon
  },
  {
    title: 'Settings',
    href: Routes.Settings,
    icon: SettingsIcon
  }
];

export const accountNavItems: NavItem[] = [
  {
    title: 'Profile',
    href: Routes.Profile,
    icon: UserIcon
  },
  {
    title: 'Security',
    href: Routes.Security,
    icon: LockKeyholeIcon
  },
  {
    title: 'Notifications',
    href: Routes.Notifications,
    icon: BellIcon
  }
];

export const organizationNavItems: NavItem[] = [
  {
    title: 'Information',
    href: Routes.OrganizationInformation,
    icon: StoreIcon
  },
  {
    title: 'Members',
    href: Routes.Members,
    icon: UserPlus2Icon
  },
  {
    title: 'Billing',
    href: Routes.Billing,
    icon: CreditCardIcon
  },
  {
    title: 'Developers',
    href: Routes.Developers,
    icon: CodeIcon
  }
];

export const superAdminNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: Routes.SuperAdmin,
    icon: BarChart3Icon
  },
  {
    title: 'Organizations',
    href: Routes.SuperAdminOrganizations,
    icon: BuildingIcon
  },
  {
    title: 'Users',
    href: Routes.SuperAdminUsers,
    icon: UsersIcon
  },
  {
    title: 'Billing',
    href: Routes.SuperAdminBilling,
    icon: CreditCardIcon
  },
  {
    title: 'System',
    href: Routes.SuperAdminSystem,
    icon: SettingsIcon
  }
];

export const systemNavItems: NavItem[] = [
  {
    title: 'General',
    href: Routes.SuperAdminSystem,
    icon: SettingsIcon
  },
  {
    title: 'Security',
    href: `${Routes.SuperAdminSystem}/security`,
    icon: ShieldIcon
  },
  {
    title: 'Email',
    href: `${Routes.SuperAdminSystem}/email`,
    icon: MailIcon
  },
  {
    title: 'Database',
    href: `${Routes.SuperAdminSystem}/database`,
    icon: DatabaseIcon
  },
  {
    title: 'API',
    href: `${Routes.SuperAdminSystem}/api`,
    icon: GlobeIcon
  }
];
