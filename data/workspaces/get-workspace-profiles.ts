import { cache } from 'react';
import { unstable_cache } from 'next/cache';

import { Caching, OrganizationCacheKey } from '@/data/caching';
import { dedupedAuth } from '@/lib/auth';
import { checkSession } from '@/lib/auth/session';
import { prisma } from '@/lib/db/prisma';

export const getWorkspaceProfiles = cache(async (workspaceId: string) => {
  const session = await dedupedAuth();
  if (!checkSession(session)) {
    return [];
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { organizationId: true }
  });

  if (!user?.organizationId) {
    return [];
  }

  // Verify workspace ownership
  const workspace = await prisma.workspace.findFirst({
    where: {
      id: workspaceId,
      organizationId: user.organizationId
    },
    select: { id: true }
  });

  if (!workspace) {
    return [];
  }

  return await unstable_cache(
    async () => {
      return await prisma.workspaceProfile.findMany({
        where: {
          workspaceId: workspaceId
        },
        select: {
          id: true,
          title: true,
          description: true,
          foundInSearch: true,
          addedAt: true,
          profile: {
            select: {
              id: true,
              linkedinUrl: true,
              profileData: true,
              scrapedAt: true
            }
          }
        },
        orderBy: {
          addedAt: 'desc'
        }
      });
    },
    [`workspace-profiles-${workspaceId}`],
    {
      tags: [
        Caching.createOrganizationTag(
          OrganizationCacheKey.WorkspaceProfiles,
          user.organizationId,
          workspaceId
        )
      ],
      revalidate: 300 // 5 minutes
    }
  )();
});
