'use client';

import * as React from 'react';
import { CheckCircleIcon, ExternalLinkIcon, EyeIcon, SparklesIcon } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface SearchResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  results: {
    totalFound: number;
    uniqueFound: number;
    newProfiles: number;
    alreadyInWorkspace: number;
    searchDorks?: string[];
  };
  onViewResults: () => void;
}

export function SearchResultsModal({ 
  isOpen, 
  onClose, 
  results, 
  onViewResults 
}: SearchResultsModalProps) {
  const { totalFound, uniqueFound, newProfiles, alreadyInWorkspace, searchDorks } = results;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <CheckCircleIcon className="size-6 text-green-600" />
            <DialogTitle className="text-xl">Search Completed Successfully!</DialogTitle>
          </div>
          <DialogDescription>
            Your AI-powered LinkedIn search has finished. Here are the results:
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Results Summary */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{totalFound}</div>
                  <div className="text-sm text-muted-foreground">Total Found</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{newProfiles}</div>
                  <div className="text-sm text-muted-foreground">New Profiles</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Breakdown */}
          <div className="space-y-3">
            <h4 className="font-medium">Detailed Breakdown:</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">📊 Total profiles found:</span>
                <Badge variant="outline">{totalFound}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">🔗 Unique profiles:</span>
                <Badge variant="outline">{uniqueFound}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">➕ New profiles added:</span>
                <Badge variant="default">{newProfiles}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">📁 Already in workspace:</span>
                <Badge variant="secondary">{alreadyInWorkspace}</Badge>
              </div>

            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 sm:flex-row">
            <Button
              onClick={onViewResults}
              className="flex-1 h-12 text-base font-medium"
              size="lg"
            >
              <EyeIcon className="size-5" />
              View All Profiles
            </Button>
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 h-12 text-base font-medium"
              size="lg"
            >
              <SparklesIcon className="size-5" />
              Continue Searching
            </Button>
          </div>

          {/* Success Message */}
          <div className="text-center text-sm text-muted-foreground">
            🎉 Great! You can now review the profiles and unlock the ones you're interested in.
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
