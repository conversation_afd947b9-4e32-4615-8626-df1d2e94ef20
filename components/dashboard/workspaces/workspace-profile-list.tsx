'use client';

import * as React from 'react';
import Link from 'next/link';
import { ExternalLinkIcon, UserIcon, BuildingIcon, LockIcon, UnlockIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { EmptyState } from '@/components/ui/empty-state';
import { formatDistanceToNow } from 'date-fns';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

interface WorkspaceProfileListProps {
  profiles: WorkspaceProfile[];
  targetType: 'PERSON' | 'COMPANY';
}

export function WorkspaceProfileList({ profiles, targetType }: WorkspaceProfileListProps) {
  const [selectedProfiles, setSelectedProfiles] = React.useState<Set<string>>(new Set());

  const toggleProfile = (profileId: string) => {
    const newSelected = new Set(selectedProfiles);
    if (newSelected.has(profileId)) {
      newSelected.delete(profileId);
    } else {
      newSelected.add(profileId);
    }
    setSelectedProfiles(newSelected);
  };

  const selectAll = () => {
    setSelectedProfiles(new Set(profiles.map(p => p.profile.id)));
  };

  const clearSelection = () => {
    setSelectedProfiles(new Set());
  };

  const extractProfileName = (title: string | null, url: string): string => {
    if (title) {
      // Extract name from title, remove " - LinkedIn" suffix
      const cleanTitle = title.replace(/ - LinkedIn.*$/, '').trim();
      if (cleanTitle) return cleanTitle;
    }

    // Fallback: extract from URL
    try {
      const urlParts = url.split('/');
      const profilePart = urlParts[urlParts.length - 2] || urlParts[urlParts.length - 1];
      return profilePart.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    } catch {
      return 'Unknown Profile';
    }
  };

  const isProfileUnlocked = (profile: WorkspaceProfile['profile']): boolean => {
    return profile.profileData !== null && profile.scrapedAt !== null;
  };

  if (profiles.length === 0) {
    return (
      <EmptyState
        icon={targetType === 'PERSON' ? UserIcon : BuildingIcon}
        title={`No ${targetType === 'PERSON' ? 'profiles' : 'companies'} found yet`}
        description={`Use the search form above to find LinkedIn ${targetType === 'PERSON' ? 'professionals' : 'companies'} for your workspace.`}
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      <div className="flex items-center justify-between rounded-lg border bg-muted/50 p-3">
        <div className="flex items-center gap-4">
          <div className="text-sm font-medium">
            {profiles.length} {targetType === 'PERSON' ? 'profiles' : 'companies'} found
          </div>
          {selectedProfiles.size > 0 && (
            <div className="text-sm text-muted-foreground">
              {selectedProfiles.size} selected
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {selectedProfiles.size > 0 ? (
            <>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear Selection
              </Button>
              <Button size="sm" disabled>
                <UnlockIcon className="size-4" />
                Unlock Selected ({selectedProfiles.size})
              </Button>
            </>
          ) : (
            <Button variant="outline" size="sm" onClick={selectAll}>
              Select All
            </Button>
          )}
        </div>
      </div>

      {/* Profile Grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {profiles.map((workspaceProfile) => {
          const { profile } = workspaceProfile;
          const isUnlocked = isProfileUnlocked(profile);
          const isSelected = selectedProfiles.has(profile.id);
          const profileName = extractProfileName(workspaceProfile.title, profile.linkedinUrl);

          return (
            <Card 
              key={profile.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => toggleProfile(profile.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {targetType === 'PERSON' ? (
                      <UserIcon className="size-5 text-blue-600" />
                    ) : (
                      <BuildingIcon className="size-5 text-green-600" />
                    )}
                    <CardTitle className="text-base line-clamp-1">
                      {profileName}
                    </CardTitle>
                  </div>
                  <div className="flex items-center gap-1">
                    {isUnlocked ? (
                      <Badge variant="default" className="gap-1">
                        <UnlockIcon className="size-3" />
                        Unlocked
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="gap-1">
                        <LockIcon className="size-3" />
                        Locked
                      </Badge>
                    )}
                  </div>
                </div>
                {workspaceProfile.description && (
                  <CardDescription className="line-clamp-2">
                    {workspaceProfile.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Found:</span>
                    <span className="text-xs">
                      {formatDistanceToNow(workspaceProfile.addedAt, { addSuffix: true })}
                    </span>
                  </div>
                  {isUnlocked && profile.scrapedAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Unlocked:</span>
                      <span className="text-xs">
                        {formatDistanceToNow(profile.scrapedAt, { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="flex-1"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link href={profile.linkedinUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLinkIcon className="size-3" />
                      View LinkedIn
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    disabled={isUnlocked}
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Implement unlock functionality
                    }}
                  >
                    {isUnlocked ? 'Unlocked' : 'Unlock'}
                  </Button>
                </div>

                {workspaceProfile.foundInSearch && (
                  <div className="rounded bg-muted p-2 text-xs">
                    <div className="font-medium text-muted-foreground mb-1">Search Query:</div>
                    <div className="font-mono text-xs break-all">
                      {workspaceProfile.foundInSearch}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Unlock Instructions */}
      <div className="rounded-lg border bg-muted/50 p-4 text-sm">
        <div className="font-medium mb-2">💡 How to unlock profiles:</div>
        <ul className="space-y-1 text-muted-foreground">
          <li>• Select individual profiles or use "Select All" for bulk operations</li>
          <li>• Click "Unlock" to get detailed profile information including contact details</li>
          <li>• Unlocked data is cached for 30 days to save costs</li>
          <li>• Each unlock uses API credits based on your subscription plan</li>
        </ul>
      </div>
    </div>
  );
}
