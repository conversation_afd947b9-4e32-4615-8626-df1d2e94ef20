'use client';

import * as React from 'react';
import Link from 'next/link';
import {
  ExternalLinkIcon,
  UserIcon,
  BuildingIcon,
  LockIcon,
  UnlockIcon,
  MoreHorizontalIcon,
  SearchIcon
} from 'lucide-react';
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState
} from '@tanstack/react-table';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DataTable,
  DataTableColumnHeader,
  DataTablePagination
} from '@/components/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { EmptyState } from '@/components/ui/empty-state';
import { useAIScoringProgress } from '@/hooks/use-ai-scoring-progress';
import { formatDistanceToNow } from 'date-fns';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  aiScore: number | null;
  aiReason: string | null;
  aiExtractedData: any;
  aiProcessedAt: Date | null;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

interface WorkspaceProfileListProps {
  profiles: WorkspaceProfile[];
  targetType: 'PERSON' | 'COMPANY';
  workspaceId: string;
}

export function WorkspaceProfileList({ profiles, targetType, workspaceId }: WorkspaceProfileListProps) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = React.useState('');

  // AI scoring progress
  const { progress, isLoading } = useAIScoringProgress(workspaceId);

  // Track if we've already refreshed to avoid infinite loops
  const [hasRefreshed, setHasRefreshed] = React.useState(false);

  // Refresh data when AI scoring completes (only once)
  React.useEffect(() => {
    if (!progress.isProcessing && progress.scoredProfiles > 0 && !hasRefreshed) {
      // Mark as refreshed to prevent infinite loops
      setHasRefreshed(true);

      // Trigger a soft refresh by reloading the page
      setTimeout(() => {
        window.location.reload();
      }, 2000); // Wait 2 seconds before refresh
    }
  }, [progress.isProcessing, progress.scoredProfiles, hasRefreshed]);

  // Utility functions
  const extractProfileName = (title: string | null, url: string): string => {
    if (title) {
      const cleanTitle = title.replace(/ - LinkedIn.*$/, '').trim();
      if (cleanTitle) return cleanTitle;
    }

    try {
      const urlParts = url.split('/');
      const profilePart = urlParts[urlParts.length - 2] || urlParts[urlParts.length - 1];
      return profilePart.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    } catch {
      return 'Unknown Profile';
    }
  };

  const isProfileUnlocked = (profile: WorkspaceProfile['profile']): boolean => {
    return profile.profileData !== null && profile.scrapedAt !== null;
  };

  const getProfileInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getScoreBadge = (score: number | null, isProcessing: boolean = false) => {
    if (score === null) {
      if (isProcessing) {
        return (
          <Badge variant="secondary" className="gap-1 text-blue-600">
            <div className="size-2 rounded-full bg-blue-400 animate-pulse" />
            ...
          </Badge>
        );
      }
      return (
        <Badge variant="secondary" className="gap-1 text-gray-500">
          <div className="size-2 rounded-full bg-gray-400" />
          0.0
        </Badge>
      );
    }

    if (score >= 8.5) {
      return (
        <Badge variant="default" className="gap-1 bg-green-600 hover:bg-green-700">
          <div className="size-2 rounded-full bg-green-200" />
          {score.toFixed(1)}
        </Badge>
      );
    }

    if (score >= 6) {
      return (
        <Badge variant="secondary" className="gap-1 bg-yellow-500 hover:bg-yellow-600 text-white">
          <div className="size-2 rounded-full bg-yellow-200" />
          {score.toFixed(1)}
        </Badge>
      );
    }

    return (
      <Badge variant="destructive" className="gap-1">
        <div className="size-2 rounded-full bg-red-200" />
        {score.toFixed(1)}
      </Badge>
    );
  };

  // Table columns definition
  const columns: ColumnDef<WorkspaceProfile>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="mx-auto flex items-center justify-center"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="mx-auto flex items-center justify-center"
          onClick={(e) => e.stopPropagation()}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
      maxSize: 40
    },
    {
      accessorKey: 'profile.linkedinUrl',
      id: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        const profile = row.original;
        const name = extractProfileName(profile.title, profile.profile.linkedinUrl);
        const initials = getProfileInitials(name);

        return (
          <div className="flex items-center gap-3">
            <Avatar className="size-8">
              <AvatarImage src="" alt={name} />
              <AvatarFallback className="text-xs">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm truncate">{name}</div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
      size: 200,
      maxSize: 250
    },
    {
      accessorKey: 'profile.linkedinUrl',
      id: 'linkedin',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="LinkedIn" />
      ),
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          asChild
          className="h-8 px-2"
        >
          <Link
            href={row.original.profile.linkedinUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLinkIcon className="size-3" />
            View
          </Link>
        </Button>
      ),
      enableSorting: false,
      enableHiding: true,
      size: 80,
      maxSize: 100
    },
    {
      accessorKey: 'aiScore',
      id: 'aiScore',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="AI Score" />
      ),
      cell: ({ row }) => {
        const score = row.original.aiScore;
        return getScoreBadge(score, progress.isProcessing);
      },
      enableSorting: true,
      enableHiding: true,
      sortingFn: (rowA, rowB) => {
        const scoreA = rowA.original.aiScore ?? -1;
        const scoreB = rowB.original.aiScore ?? -1;
        return scoreB - scoreA; // Descending order (highest first)
      },
      size: 90,
      maxSize: 110,
      meta: {
        headerClassName: "text-center",
        cellClassName: "text-center"
      }
    },
    {
      accessorKey: 'profile.scrapedAt',
      id: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const isUnlocked = isProfileUnlocked(row.original.profile);
        return (
          <Badge variant={isUnlocked ? 'default' : 'secondary'} className="gap-1">
            {isUnlocked ? (
              <>
                <UnlockIcon className="size-3" />
                Unlocked
              </>
            ) : (
              <>
                <LockIcon className="size-3" />
                Locked
              </>
            )}
          </Badge>
        );
      },
      enableSorting: true,
      enableHiding: true,
      size: 100,
      maxSize: 120
    },
    {
      accessorKey: 'addedAt',
      id: 'addedAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Added" />
      ),
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {formatDistanceToNow(row.original.addedAt, { addSuffix: true })}
        </span>
      ),
      enableSorting: true,
      enableHiding: true,
      size: 120,
      maxSize: 140
    },
    {
      id: 'actions',
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => {
        const profile = row.original;
        const isUnlocked = isProfileUnlocked(profile.profile);

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontalIcon className="size-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link
                  href={profile.profile.linkedinUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <ExternalLinkIcon className="size-4" />
                  View LinkedIn
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled={isUnlocked}>
                <UnlockIcon className="size-4" />
                {isUnlocked ? 'Already Unlocked' : 'Unlock Profile'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 40,
      maxSize: 50
    }
  ];

  // Table setup
  const table = useReactTable({
    data: profiles,
    columns,
    state: {
      rowSelection,
      columnVisibility,
      columnFilters,
      globalFilter
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onColumnVisibilityChange: setColumnVisibility,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 10
      },
      sorting: [
        {
          id: 'aiScore',
          desc: false // Lowest scores first (ASC)
        }
      ]
    }
  });

  const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

  if (profiles.length === 0) {
    return (
      <div className="rounded-lg border bg-card p-8">
        <EmptyState
          icon={targetType === 'PERSON' ? <UserIcon className="size-12 text-muted-foreground" /> : <BuildingIcon className="size-12 text-muted-foreground" />}
          title={`No ${targetType === 'PERSON' ? 'profiles' : 'companies'} found yet`}
          description={`Use the Search & Discover tab to find LinkedIn ${targetType === 'PERSON' ? 'professionals' : 'companies'} for your workspace.`}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Processing Indicator */}
      {progress.isProcessing && (
        <div className="rounded-lg border bg-blue-50 border-blue-200">
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="size-5 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
              <div className="flex-1">
                <div className="font-medium text-blue-900">🤖 AI is analyzing profiles...</div>
                <div className="text-sm text-blue-700">
                  Processing {progress.scoredProfiles} of {progress.totalProfiles} profiles ({Math.round(progress.progress)}% complete)
                </div>
              </div>
              <div className="text-sm font-medium text-blue-900">
                {Math.round(progress.progress)}%
              </div>
            </div>
            <div className="mt-3 w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.progress}%` }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="rounded-lg border bg-card">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Found Profiles</h2>
              <p className="text-sm text-muted-foreground">
                {table.getFilteredRowModel().rows.length} of {profiles.length} {targetType === 'PERSON' ? 'profiles' : 'companies'}
                {progress.isProcessing && (
                  <span className="ml-2 text-blue-600">• AI scoring in progress</span>
                )}
              </p>
            </div>
            {hasSelectedRows && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {table.getSelectedRowModel().rows.length} selected
                </span>
                <Button size="sm" disabled>
                  <UnlockIcon className="size-4" />
                  Unlock Selected
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="p-4">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder={`Search ${targetType === 'PERSON' ? 'profiles' : 'companies'}...`}
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-10 max-w-sm"
            />
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-lg border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <DataTable
            table={table}
            wrapperClassName="min-h-[400px]"
          />
        </div>
        <div className="border-t">
          <DataTablePagination table={table} />
        </div>
      </div>

      {/* Unlock Instructions */}
      <div className="rounded-lg border bg-muted/50 p-4 text-sm">
        <div className="font-medium mb-2">💡 How to unlock profiles:</div>
        <ul className="space-y-1 text-muted-foreground">
          <li>• Use the search and filters above to find specific profiles</li>
          <li>• Select individual profiles or use "Select All" for bulk operations</li>
          <li>• Click "Unlock" to get detailed profile information including contact details</li>
          <li>• Unlocked data is cached for 30 days to save costs</li>
          <li>• Each unlock uses API credits based on your subscription plan</li>
        </ul>
      </div>
    </div>
  );
}
