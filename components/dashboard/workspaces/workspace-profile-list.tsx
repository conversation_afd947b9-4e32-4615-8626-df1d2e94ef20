'use client';

import * as React from 'react';
import Link from 'next/link';
import {
  ExternalLinkIcon,
  UserIcon,
  BuildingIcon,
  LockIcon,
  UnlockIcon,
  FilterIcon,
  SearchIcon,
  LayoutGridIcon,
  ListIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { EmptyState } from '@/components/ui/empty-state';
import { formatDistanceToNow } from 'date-fns';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

interface WorkspaceProfileListProps {
  profiles: WorkspaceProfile[];
  targetType: 'PERSON' | 'COMPANY';
  workspaceId: string;
}

export function WorkspaceProfileList({ profiles, targetType, workspaceId }: WorkspaceProfileListProps) {
  const [selectedProfiles, setSelectedProfiles] = React.useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<'all' | 'locked' | 'unlocked'>('all');
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = React.useState(1);
  const itemsPerPage = 12;

  const toggleProfile = (profileId: string) => {
    const newSelected = new Set(selectedProfiles);
    if (newSelected.has(profileId)) {
      newSelected.delete(profileId);
    } else {
      newSelected.add(profileId);
    }
    setSelectedProfiles(newSelected);
  };

  const selectAll = () => {
    setSelectedProfiles(new Set(profiles.map(p => p.profile.id)));
  };

  const clearSelection = () => {
    setSelectedProfiles(new Set());
  };

  const extractProfileName = (title: string | null, url: string): string => {
    if (title) {
      // Extract name from title, remove " - LinkedIn" suffix
      const cleanTitle = title.replace(/ - LinkedIn.*$/, '').trim();
      if (cleanTitle) return cleanTitle;
    }

    // Fallback: extract from URL
    try {
      const urlParts = url.split('/');
      const profilePart = urlParts[urlParts.length - 2] || urlParts[urlParts.length - 1];
      return profilePart.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    } catch {
      return 'Unknown Profile';
    }
  };

  const isProfileUnlocked = (profile: WorkspaceProfile['profile']): boolean => {
    return profile.profileData !== null && profile.scrapedAt !== null;
  };

  // Filter profiles based on search and status
  const filteredProfiles = React.useMemo(() => {
    return profiles.filter(workspaceProfile => {
      const profileName = extractProfileName(workspaceProfile.title, workspaceProfile.profile.linkedinUrl);
      const matchesSearch = searchQuery === '' ||
        profileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (workspaceProfile.description && workspaceProfile.description.toLowerCase().includes(searchQuery.toLowerCase()));

      const isUnlocked = isProfileUnlocked(workspaceProfile.profile);
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'locked' && !isUnlocked) ||
        (statusFilter === 'unlocked' && isUnlocked);

      return matchesSearch && matchesStatus;
    });
  }, [profiles, searchQuery, statusFilter]);

  // Pagination
  const totalPages = Math.ceil(filteredProfiles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProfiles = filteredProfiles.slice(startIndex, startIndex + itemsPerPage);

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, statusFilter]);

  if (profiles.length === 0) {
    return (
      <div className="space-y-6">
        <div className="rounded-lg border bg-card p-8">
          <EmptyState
            icon={targetType === 'PERSON' ? <UserIcon className="size-12 text-muted-foreground" /> : <BuildingIcon className="size-12 text-muted-foreground" />}
            title={`No ${targetType === 'PERSON' ? 'profiles' : 'companies'} found yet`}
            description={`Use the Search & Discover tab to find LinkedIn ${targetType === 'PERSON' ? 'professionals' : 'companies'} for your workspace.`}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="rounded-lg border bg-card">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Found Profiles</h2>
              <p className="text-sm text-muted-foreground">
                {filteredProfiles.length} of {profiles.length} {targetType === 'PERSON' ? 'profiles' : 'companies'}
                {searchQuery && ` matching "${searchQuery}"`}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <LayoutGridIcon className="size-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <ListIcon className="size-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="p-4 space-y-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search profiles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="locked">Locked</SelectItem>
                  <SelectItem value="unlocked">Unlocked</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Bulk Actions */}
          {filteredProfiles.length > 0 && (
            <div className="flex items-center justify-between rounded-lg bg-muted/50 p-3">
              <div className="flex items-center gap-4">
                {selectedProfiles.size > 0 && (
                  <div className="text-sm text-muted-foreground">
                    {selectedProfiles.size} selected
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {selectedProfiles.size > 0 ? (
                  <>
                    <Button variant="outline" size="sm" onClick={clearSelection}>
                      Clear Selection
                    </Button>
                    <Button size="sm" disabled>
                      <UnlockIcon className="size-4" />
                      Unlock Selected ({selectedProfiles.size})
                    </Button>
                  </>
                ) : (
                  <Button variant="outline" size="sm" onClick={selectAll}>
                    Select All
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Profile Grid/List */}
      {filteredProfiles.length === 0 ? (
        <div className="rounded-lg border bg-card p-8">
          <EmptyState
            icon={<SearchIcon className="size-12 text-muted-foreground" />}
            title="No profiles match your filters"
            description="Try adjusting your search query or filters to find more profiles."
          />
        </div>
      ) : (
        <>
          <div className={`grid gap-4 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              : 'grid-cols-1'
          }`}>
            {paginatedProfiles.map((workspaceProfile) => {
          const { profile } = workspaceProfile;
          const isUnlocked = isProfileUnlocked(profile);
          const isSelected = selectedProfiles.has(profile.id);
          const profileName = extractProfileName(workspaceProfile.title, profile.linkedinUrl);

          return (
            <Card 
              key={profile.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => toggleProfile(profile.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {targetType === 'PERSON' ? (
                      <UserIcon className="size-5 text-blue-600" />
                    ) : (
                      <BuildingIcon className="size-5 text-green-600" />
                    )}
                    <CardTitle className="text-base line-clamp-1">
                      {profileName}
                    </CardTitle>
                  </div>
                  <div className="flex items-center gap-1">
                    {isUnlocked ? (
                      <Badge variant="default" className="gap-1">
                        <UnlockIcon className="size-3" />
                        Unlocked
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="gap-1">
                        <LockIcon className="size-3" />
                        Locked
                      </Badge>
                    )}
                  </div>
                </div>
                {workspaceProfile.description && (
                  <CardDescription className="line-clamp-2">
                    {workspaceProfile.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Found:</span>
                    <span className="text-xs">
                      {formatDistanceToNow(workspaceProfile.addedAt, { addSuffix: true })}
                    </span>
                  </div>
                  {isUnlocked && profile.scrapedAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Unlocked:</span>
                      <span className="text-xs">
                        {formatDistanceToNow(profile.scrapedAt, { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="flex-1"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link href={profile.linkedinUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1">
                      <ExternalLinkIcon className="size-3" />
                      View LinkedIn
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    disabled={isUnlocked}
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Implement unlock functionality
                    }}
                  >
                    {isUnlocked ? 'Unlocked' : 'Unlock'}
                  </Button>
                </div>

                {workspaceProfile.foundInSearch && (
                  <div className="rounded bg-muted p-2 text-xs">
                    <div className="font-medium text-muted-foreground mb-1">Search Query:</div>
                    <div className="font-mono text-xs break-all">
                      {workspaceProfile.foundInSearch}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
            })}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between rounded-lg border bg-card p-4">
              <div className="text-sm text-muted-foreground">
                Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredProfiles.length)} of {filteredProfiles.length} profiles
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeftIcon className="size-4" />
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page =>
                      page === 1 ||
                      page === totalPages ||
                      Math.abs(page - currentPage) <= 1
                    )
                    .map((page, index, array) => (
                      <React.Fragment key={page}>
                        {index > 0 && array[index - 1] !== page - 1 && (
                          <span className="px-2 text-muted-foreground">...</span>
                        )}
                        <Button
                          variant={currentPage === page ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      </React.Fragment>
                    ))}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRightIcon className="size-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Unlock Instructions */}
      <div className="rounded-lg border bg-muted/50 p-4 text-sm">
        <div className="font-medium mb-2">💡 How to unlock profiles:</div>
        <ul className="space-y-1 text-muted-foreground">
          <li>• Use the search and filters above to find specific profiles</li>
          <li>• Select individual profiles or use "Select All" for bulk operations</li>
          <li>• Click "Unlock" to get detailed profile information including contact details</li>
          <li>• Unlocked data is cached for 30 days to save costs</li>
          <li>• Each unlock uses API credits based on your subscription plan</li>
        </ul>
      </div>
    </div>
  );
}
