'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { UserIcon, BuildingIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioCards, RadioCardItem } from '@/components/ui/radio-cards';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useZodForm } from '@/hooks/use-zod-form';
import { createWorkspaceSchema, type CreateWorkspaceSchema } from '@/schemas/workspaces/create-workspace-schema';
import { createWorkspace } from '@/actions/workspaces/create-workspace';
import { Routes } from '@/constants/routes';
import { toast } from 'sonner';

const targetTypes = [
  {
    value: 'PERSON' as const,
    label: 'People',
    description: 'Find individual professionals and decision makers',
    icon: <UserIcon className="size-5" />
  },
  {
    value: 'COMPANY' as const,
    label: 'Companies',
    description: 'Find businesses and organizations',
    icon: <BuildingIcon className="size-5" />
  }
];

const industries = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
  'Real Estate',
  'Marketing',
  'Consulting',
  'Food & Beverage',
  'Other'
];

const countries = [
  { code: 'tr', name: 'Turkey' },
  { code: 'us', name: 'United States' },
  { code: 'uk', name: 'United Kingdom' },
  { code: 'de', name: 'Germany' },
  { code: 'fr', name: 'France' }
];

const languages = [
  { code: 'tr', name: 'Turkish' },
  { code: 'en', name: 'English' },
  { code: 'de', name: 'German' },
  { code: 'fr', name: 'French' }
];

export function CreateWorkspaceForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useZodForm({
    schema: createWorkspaceSchema,
    defaultValues: {
      name: '',
      description: '',
      targetType: 'PERSON',
      industry: '',
      country: 'tr',
      language: 'tr'
    }
  });

  const onSubmit = async (data: CreateWorkspaceSchema) => {
    setIsSubmitting(true);
    try {
      const result = await createWorkspace(data);
      
      if (result?.data) {
        toast.success('Workspace created successfully!');
        router.push(`/dashboard/workspaces/${result.data.id}`);
      } else {
        toast.error('Failed to create workspace');
      }
    } catch (error) {
      console.error('Error creating workspace:', error);
      toast.error('Failed to create workspace');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Workspace Details</CardTitle>
        <CardDescription>
          Configure your workspace settings to optimize lead generation for your specific needs.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Workspace Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Healthcare Professionals, Tech Startups"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the purpose of this workspace and what type of leads you're looking for..."
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="targetType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Target Type</FormLabel>
                  <FormControl>
                    <RadioCards
                      value={field.value}
                      onValueChange={field.onChange}
                      className="grid grid-cols-1 gap-4 sm:grid-cols-2"
                      disabled={isSubmitting}
                    >
                      {targetTypes.map((type) => (
                        <RadioCardItem
                          key={type.value}
                          value={type.value}
                          className="p-4"
                        >
                          <div className="flex items-start gap-3">
                            {type.icon}
                            <div className="space-y-1">
                              <div className="font-medium">{type.label}</div>
                              <div className="text-sm text-muted-foreground">
                                {type.description}
                              </div>
                            </div>
                          </div>
                        </RadioCardItem>
                      ))}
                    </RadioCards>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {industries.map((industry) => (
                          <SelectItem key={industry} value={industry}>
                            {industry}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="language"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Language</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {languages.map((language) => (
                        <SelectItem key={language.code} value={language.code}>
                          {language.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(Routes.Workspaces)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} loading={isSubmitting}>
                Create Workspace
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
