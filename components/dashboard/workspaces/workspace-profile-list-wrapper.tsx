'use client';

import { WorkspaceProfileList } from './workspace-profile-list';
import { useWorkspaceProfiles } from '@/hooks/use-workspace-profiles';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  aiScore: number | null;
  aiReason: string | null;
  aiExtractedData: any;
  aiProcessedAt: Date | null;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

interface WorkspaceProfileListWrapperProps {
  initialProfiles: WorkspaceProfile[];
  targetType: 'PERSON' | 'COMPANY';
  workspaceId: string;
}

export function WorkspaceProfileListWrapper({
  initialProfiles,
  targetType,
  workspaceId
}: WorkspaceProfileListWrapperProps) {
  const { profiles } = useWorkspaceProfiles(workspaceId, initialProfiles);

  return (
    <WorkspaceProfileList
      profiles={profiles}
      targetType={targetType}
      workspaceId={workspaceId}
    />
  );
}
