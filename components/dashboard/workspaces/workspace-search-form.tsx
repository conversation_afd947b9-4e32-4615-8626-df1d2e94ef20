'use client';

import * as React from 'react';
import { PlusIcon, SearchIcon, XIcon } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { FormProvider, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useZodForm } from '@/hooks/use-zod-form';
import { searchWorkspaceSchema, type SearchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';
import { searchLinkedInProfiles } from '@/actions/workspaces/search-linkedin-profiles';
import { toast } from 'sonner';

interface WorkspaceSearchFormProps {
  workspaceId: string;
  targetType: 'PERSON' | 'COMPANY';
  defaultCountry?: string;
  defaultLanguage?: string;
}

export function WorkspaceSearchForm({ 
  workspaceId, 
  targetType,
  defaultCountry,
  defaultLanguage 
}: WorkspaceSearchFormProps) {
  const [isSearching, setIsSearching] = React.useState(false);
  const [locations, setLocations] = React.useState<string[]>([]);
  const [newLocation, setNewLocation] = React.useState('');

  const form = useZodForm({
    schema: searchWorkspaceSchema,
    defaultValues: {
      workspaceId,
      query: '',
      locations: [],
      includeVariations: true,
      maxResults: 50
    }
  });

  const addLocation = () => {
    if (newLocation.trim() && !locations.includes(newLocation.trim())) {
      const updatedLocations = [...locations, newLocation.trim()];
      setLocations(updatedLocations);
      form.setValue('locations', updatedLocations);
      setNewLocation('');
    }
  };

  const removeLocation = (location: string) => {
    const updatedLocations = locations.filter(l => l !== location);
    setLocations(updatedLocations);
    form.setValue('locations', updatedLocations);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addLocation();
    }
  };

  const onSubmit = async (data: SearchWorkspaceSchema) => {
    setIsSearching(true);
    try {
      const result = await searchLinkedInProfiles(data);
      
      if (result?.data) {
        toast.success(
          `Search completed! Found ${result.data.totalFound} results, added ${result.data.newProfiles} new profiles.`
        );
        
        // Reset form
        form.reset({
          workspaceId,
          query: '',
          locations: [],
          includeVariations: true,
          maxResults: 50
        });
        setLocations([]);
        
        // Refresh the page to show new results
        window.location.reload();
      } else {
        toast.error('Search failed. Please try again.');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please check your query and try again.');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="query"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Search Query</FormLabel>
              <FormControl>
                <Input
                  placeholder={
                    targetType === 'PERSON' 
                      ? 'e.g., "diyetisyen", "yazılım geliştirici", "dijital pazarlama uzmanı"'
                      : 'e.g., "yazılım şirketi", "dijital ajans", "e-ticaret"'
                  }
                  disabled={isSearching}
                  {...field}
                />
              </FormControl>
              <div className="text-xs text-muted-foreground">
                Describe the type of {targetType === 'PERSON' ? 'professionals' : 'companies'} you're looking for
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-3">
          <FormLabel>Locations (Optional)</FormLabel>
          <div className="flex gap-2">
            <Input
              placeholder="e.g., İstanbul, Ankara, İzmir"
              value={newLocation}
              onChange={(e) => setNewLocation(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isSearching}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addLocation}
              disabled={!newLocation.trim() || isSearching}
            >
              <PlusIcon className="size-4" />
              Add
            </Button>
          </div>
          
          {locations.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {locations.map((location) => (
                <Badge key={location} variant="secondary" className="gap-1">
                  {location}
                  <button
                    type="button"
                    onClick={() => removeLocation(location)}
                    disabled={isSearching}
                    className="ml-1 hover:text-destructive"
                  >
                    <XIcon className="size-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
          <div className="text-xs text-muted-foreground">
            Add specific cities or regions to narrow your search
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="includeVariations"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSearching}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Include Variations</FormLabel>
                  <div className="text-xs text-muted-foreground">
                    Use AI to generate keyword variations and synonyms
                  </div>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="maxResults"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Results</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={10}
                    max={100}
                    disabled={isSearching}
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 50)}
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">
                  Maximum number of profiles to find (10-100)
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-4 pt-4">
          <Button 
            type="submit" 
            disabled={isSearching || !form.watch('query')}
            loading={isSearching}
            className="flex-1"
          >
            <SearchIcon className="size-4" />
            {isSearching ? 'Searching...' : 'Search LinkedIn Profiles'}
          </Button>
        </div>

        {isSearching && (
          <div className="rounded-lg bg-muted p-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              <span>Generating AI-powered search queries and scanning LinkedIn...</span>
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              This may take 30-60 seconds depending on the number of locations and search complexity.
            </div>
          </div>
        )}
      </form>
    </FormProvider>
  );
}
