'use client';

import * as React from 'react';
import { PlusIcon, SearchIcon, XIcon, SparklesIcon, MapPinIcon, SettingsIcon } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { FormProvider, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useZodForm } from '@/hooks/use-zod-form';
import { searchWorkspaceSchema, type SearchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';
import { searchLinkedInProfiles } from '@/actions/workspaces/search-linkedin-profiles';
import { SearchResultsModal } from '@/components/dashboard/workspaces/search-results-modal';
import { toast } from 'sonner';

interface WorkspaceSearchFormProps {
  workspaceId: string;
  targetType: 'PERSON' | 'COMPANY';
  defaultCountry?: string;
  defaultLanguage?: string;
}

export function WorkspaceSearchForm({
  workspaceId,
  targetType,
  defaultCountry,
  defaultLanguage
}: WorkspaceSearchFormProps) {
  const [isSearching, setIsSearching] = React.useState(false);
  const [locations, setLocations] = React.useState<string[]>([]);
  const [newLocation, setNewLocation] = React.useState('');
  const [showResultsModal, setShowResultsModal] = React.useState(false);
  const [searchResults, setSearchResults] = React.useState<any>(null);

  const form = useZodForm({
    schema: searchWorkspaceSchema,
    defaultValues: {
      workspaceId,
      query: '',
      locations: [],
      includeVariations: true,
      maxResults: 50
    }
  });

  const addLocation = () => {
    if (newLocation.trim() && !locations.includes(newLocation.trim()) && locations.length < 3) {
      const updatedLocations = [...locations, newLocation.trim()];
      setLocations(updatedLocations);
      form.setValue('locations', updatedLocations);
      setNewLocation('');
    }
  };

  const removeLocation = (location: string) => {
    const updatedLocations = locations.filter(l => l !== location);
    setLocations(updatedLocations);
    form.setValue('locations', updatedLocations);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addLocation();
    }
  };

  const onSubmit = async (data: SearchWorkspaceSchema) => {
    setIsSearching(true);

    // Detect if this should be a Google Maps search
    const isLocalBusinessQuery = detectLocalBusinessQuery(data.query, targetType);

    if (isLocalBusinessQuery) {
      toast.info('📍 Searching local businesses on Google Maps...');
      await handleGoogleMapsSearch(data);
    } else {
      toast.info('🤖 AI is analyzing your query and generating search strategies...');
      await handleLinkedInSearch(data);
    }
  };

  const detectLocalBusinessQuery = (query: string, targetType: string): boolean => {
    // If target type is COMPANY and query contains local business keywords
    if (targetType !== 'COMPANY') return false;

    const localBusinessKeywords = [
      'restaurant', 'cafe', 'shop', 'store', 'clinic', 'salon', 'gym',
      'hotel', 'bar', 'bakery', 'pharmacy', 'dentist', 'doctor',
      'restoran', 'kafe', 'mağaza', 'dükkan', 'klinik', 'berber',
      'eczane', 'doktor', 'diş', 'kuaför', 'spor salonu'
    ];

    const queryLower = query.toLowerCase();
    return localBusinessKeywords.some(keyword => queryLower.includes(keyword));
  };

  const handleGoogleMapsSearch = async (data: SearchWorkspaceSchema) => {
    try {
      console.log('Using Google Maps search for local businesses');

      // Use first location for Google Maps search
      const location = data.locations[0];

      const response = await fetch('/api/google-maps/bulk-scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          query: data.query.trim(),
          location: location,
          limit: 20
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Google Maps search completed:', result);

        toast.success(`📍 Found ${result.businessesFound} local businesses!`);

        // Reset form
        form.reset({
          workspaceId,
          query: '',
          locations: []
        });
        setLocations([]);

        // Refresh to show new contacts
        setTimeout(() => window.location.reload(), 1000);
      } else {
        console.error('Google Maps search failed');
        toast.error('❌ Google Maps search failed. Please try again.');
      }
    } catch (error) {
      console.error('Google Maps search error:', error);
      toast.error('❌ Search failed. Please check your query and try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleLinkedInSearch = async (data: SearchWorkspaceSchema) => {
    try {
      console.log('Using LinkedIn search for professional/company data');

      const result = await searchLinkedInProfiles(data);

      if (result?.data) {
        const { totalFound, uniqueFound, newProfiles, alreadyInWorkspace, searchDorks } = result.data;

        // Store results and show modal
        setSearchResults(result.data);
        setShowResultsModal(true);

        // Reset form
        form.reset({
          workspaceId,
          query: '',
          locations: []
        });
        setLocations([]);

        // No page refresh - let the table update naturally
      } else {
        toast.error('❌ Search failed. Please try again.');
      }
    } catch (error) {
      console.error('LinkedIn search error:', error);
      toast.error('❌ Search failed. Please check your query and try again.');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <SparklesIcon className="size-5 text-primary" />
          <h3 className="text-lg font-semibold">AI-Powered Search</h3>
        </div>
        <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
          Describe what you're looking for and our AI will generate optimized LinkedIn search queries to find the best {targetType === 'PERSON' ? 'professionals' : 'companies'}.
        </p>
      </div>

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Main Search Input */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <SearchIcon className="size-4" />
                What are you looking for?
              </CardTitle>
              <CardDescription>
                Be specific about the type of {targetType === 'PERSON' ? 'professionals' : 'companies'} you want to find
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="query"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder={
                          targetType === 'PERSON'
                            ? 'e.g., "Nutrition consultants who own their practice"'
                            : 'e.g., "Software agencies specializing in SaaS development"'
                        }
                        disabled={isSearching}
                        className="h-10" // Standard height
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Locations */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <MapPinIcon className="size-4" />
                Target Locations
              </CardTitle>
              <CardDescription>
                Add specific cities or regions to focus your search (max 3 locations)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="e.g., İstanbul, New York, London"
                  value={newLocation}
                  onChange={(e) => setNewLocation(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isSearching}
                  className="flex-1 h-10" // Standard height
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addLocation}
                  disabled={!newLocation.trim() || isSearching || locations.length >= 3}
                  className="h-10" // Match input height
                >
                  <PlusIcon className="size-4" />
                  Add
                </Button>
              </div>

              {locations.length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">Selected locations:</div>
                  <div className="flex flex-wrap gap-2">
                    {locations.map((location) => (
                      <Badge key={location} variant="secondary" className="gap-1 px-3 py-1">
                        <MapPinIcon className="size-3" />
                        {location}
                        <button
                          type="button"
                          onClick={() => removeLocation(location)}
                          disabled={isSearching}
                          className="ml-1 hover:text-destructive transition-colors"
                        >
                          <XIcon className="size-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI will automatically optimize search parameters */}

          {/* Search Button */}
          <div className="flex justify-center pt-2">
            <Button
              type="submit"
              disabled={isSearching || !form.watch('query')}
              loading={isSearching}
              className="min-w-[200px] h-10" // Standard height
            >
              <SparklesIcon className="size-4" />
              {isSearching ? 'Searching...' : 'Start AI Search'}
            </Button>
          </div>

          {/* Loading State */}
          {isSearching && (
            <div
              className="mt-6"
              ref={(el) => {
                if (el) {
                  // Scroll to loading state when it appears
                  setTimeout(() => {
                    el.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                  }, 300);
                }
              }}
            >
              <Card className="border-primary/20 bg-primary/5">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-3">
                    <div className="size-5 animate-spin rounded-full border-2 border-primary border-t-transparent mt-0.5" />
                    <div className="space-y-2">
                      <div className="font-medium text-sm">🤖 AI is working on your search...</div>
                      <div className="text-xs text-muted-foreground space-y-1">
                        <div>• Analyzing your query and generating optimized search terms</div>
                        <div>• Scanning LinkedIn for matching {targetType === 'PERSON' ? 'professionals' : 'companies'}</div>
                        <div>• This typically takes 30-60 seconds</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </form>
      </FormProvider>

      {/* Results Modal */}
      {searchResults && (
        <SearchResultsModal
          isOpen={showResultsModal}
          onClose={() => setShowResultsModal(false)}
          results={searchResults}
          onViewResults={() => {
            setShowResultsModal(false);
            // Switch to profiles tab
            const profilesTab = document.querySelector('[data-value="profiles"]') as HTMLElement;
            if (profilesTab) {
              profilesTab.click();
            }
          }}
        />
      )}
    </div>
  );
}
