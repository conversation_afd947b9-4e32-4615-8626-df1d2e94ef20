'use client';

import * as React from 'react';
import { PlusIcon, SearchIcon, XIcon, SparklesIcon, MapPinIcon, SettingsIcon } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { FormProvider, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useZodForm } from '@/hooks/use-zod-form';
import { searchWorkspaceSchema, type SearchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';
import { searchLinkedInProfiles } from '@/actions/workspaces/search-linkedin-profiles';
import { toast } from 'sonner';

interface WorkspaceSearchFormProps {
  workspaceId: string;
  targetType: 'PERSON' | 'COMPANY';
  defaultCountry?: string;
  defaultLanguage?: string;
}

export function WorkspaceSearchForm({
  workspaceId,
  targetType,
  defaultCountry,
  defaultLanguage
}: WorkspaceSearchFormProps) {
  const [isSearching, setIsSearching] = React.useState(false);
  const [locations, setLocations] = React.useState<string[]>([]);
  const [newLocation, setNewLocation] = React.useState('');

  const form = useZodForm({
    schema: searchWorkspaceSchema,
    defaultValues: {
      workspaceId,
      query: '',
      locations: [],
      includeVariations: true,
      maxResults: 50
    }
  });

  const addLocation = () => {
    if (newLocation.trim() && !locations.includes(newLocation.trim())) {
      const updatedLocations = [...locations, newLocation.trim()];
      setLocations(updatedLocations);
      form.setValue('locations', updatedLocations);
      setNewLocation('');
    }
  };

  const removeLocation = (location: string) => {
    const updatedLocations = locations.filter(l => l !== location);
    setLocations(updatedLocations);
    form.setValue('locations', updatedLocations);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addLocation();
    }
  };

  const onSubmit = async (data: SearchWorkspaceSchema) => {
    setIsSearching(true);

    // Show immediate feedback
    toast.info('🤖 AI is analyzing your query and generating search strategies...');

    try {
      const result = await searchLinkedInProfiles(data);

      if (result?.data) {
        const { totalFound, uniqueFound, newProfiles, alreadyInWorkspace, searchDorks } = result.data;

        // Show detailed success message with action
        toast.success(
          `✅ Search completed!\n` +
          `📊 Found: ${totalFound} profiles\n` +
          `🔗 Unique: ${uniqueFound}\n` +
          `➕ New: ${newProfiles}\n` +
          `📁 Already in workspace: ${alreadyInWorkspace}\n` +
          `🔍 Search queries used: ${searchDorks?.length || 0}\n\n` +
          `Click "Found Profiles" tab to see results!`,
          {
            duration: 8000,
            action: {
              label: 'View Results',
              onClick: () => {
                // Switch to profiles tab
                const profilesTab = document.querySelector('[data-value="profiles"]') as HTMLElement;
                if (profilesTab) {
                  profilesTab.click();
                }
              }
            }
          }
        );

        // Reset form but don't reload page
        form.reset({
          workspaceId,
          query: '',
          locations: [],
          includeVariations: true,
          maxResults: 50
        });
        setLocations([]);

        // Just refresh the data without full page reload
        // The cache invalidation will handle the updates
      } else {
        toast.error('❌ Search failed. Please try again.');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error('❌ Search failed. Please check your query and try again.');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <SparklesIcon className="size-5 text-primary" />
          <h3 className="text-lg font-semibold">AI-Powered Search</h3>
        </div>
        <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
          Describe what you're looking for and our AI will generate optimized LinkedIn search queries to find the best {targetType === 'PERSON' ? 'professionals' : 'companies'}.
        </p>
      </div>

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Main Search Input */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <SearchIcon className="size-4" />
                What are you looking for?
              </CardTitle>
              <CardDescription>
                Be specific about the type of {targetType === 'PERSON' ? 'professionals' : 'companies'} you want to find
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="query"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder={
                          targetType === 'PERSON'
                            ? 'e.g., "Nutrition consultants who own their practice"'
                            : 'e.g., "Software agencies specializing in SaaS development"'
                        }
                        disabled={isSearching}
                        className="h-10" // Standard height
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Locations */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <MapPinIcon className="size-4" />
                Target Locations
              </CardTitle>
              <CardDescription>
                Add specific cities or regions to focus your search (optional)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="e.g., İstanbul, New York, London"
                  value={newLocation}
                  onChange={(e) => setNewLocation(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isSearching}
                  className="flex-1 h-10" // Standard height
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addLocation}
                  disabled={!newLocation.trim() || isSearching}
                  className="h-10" // Match input height
                >
                  <PlusIcon className="size-4" />
                  Add
                </Button>
              </div>

              {locations.length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">Selected locations:</div>
                  <div className="flex flex-wrap gap-2">
                    {locations.map((location) => (
                      <Badge key={location} variant="secondary" className="gap-1 px-3 py-1">
                        <MapPinIcon className="size-3" />
                        {location}
                        <button
                          type="button"
                          onClick={() => removeLocation(location)}
                          disabled={isSearching}
                          className="ml-1 hover:text-destructive transition-colors"
                        >
                          <XIcon className="size-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Advanced Settings */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <SettingsIcon className="size-4" />
                Search Settings
              </CardTitle>
              <CardDescription>
                Fine-tune your search parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="includeVariations"
                  render={({ field }) => (
                    <FormItem className="rounded-lg border p-4">
                      <div className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isSearching}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-medium">
                            AI Keyword Expansion
                          </FormLabel>
                          <div className="text-xs text-muted-foreground">
                            Generate synonyms and variations automatically
                          </div>
                        </div>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxResults"
                  render={({ field }) => (
                    <FormItem className="rounded-lg border p-4">
                      <FormLabel className="text-sm font-medium">Max Results</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={10}
                          max={100}
                          disabled={isSearching}
                          className="h-10 mt-2" // Standard height with margin
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 50)}
                        />
                      </FormControl>
                      <div className="text-xs text-muted-foreground mt-1">
                        Profiles to find per search (10-100)
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Search Button */}
          <div className="flex justify-center pt-2">
            <Button
              type="submit"
              disabled={isSearching || !form.watch('query')}
              loading={isSearching}
              className="min-w-[200px] h-10" // Standard height
            >
              <SparklesIcon className="size-4" />
              {isSearching ? 'Searching...' : 'Start AI Search'}
            </Button>
          </div>

          {/* Loading State */}
          {isSearching && (
            <Card
              className="border-primary/20 bg-primary/5"
              ref={(el) => {
                if (el) {
                  // Scroll to loading state when it appears
                  setTimeout(() => {
                    el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  }, 100);
                }
              }}
            >
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <div className="size-5 animate-spin rounded-full border-2 border-primary border-t-transparent mt-0.5" />
                  <div className="space-y-2">
                    <div className="font-medium text-sm">🤖 AI is working on your search...</div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div>• Analyzing your query and generating optimized search terms</div>
                      <div>• Scanning LinkedIn for matching {targetType === 'PERSON' ? 'professionals' : 'companies'}</div>
                      <div>• This typically takes 30-60 seconds</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </form>
      </FormProvider>
    </div>
  );
}
